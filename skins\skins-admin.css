#trx_addons_theme_panel_section_skins .trx_addons_image_block.skin_installed .trx_addons_image_block_image, #trx_addons_theme_panel_section_skins .trx_addons_image_block.skin_new .trx_addons_image_block_image, #trx_addons_theme_panel_section_skins .trx_addons_image_block.skin_updated .trx_addons_image_block_image {
  overflow: hidden;
}
#trx_addons_theme_panel_section_skins .trx_addons_image_block.skin_installed .skin_label, #trx_addons_theme_panel_section_skins .trx_addons_image_block.skin_new .skin_label, #trx_addons_theme_panel_section_skins .trx_addons_image_block.skin_updated .skin_label {
  display: block;
  position: absolute;
  z-index: 2000;
  top: 0;
  right: 0;
  font-size: 10px;
  font-weight: bold;
  text-transform: uppercase;
  background-color: #11a0d2;
  color: #fff;
  text-align: center;
  padding: 0 1em;
}
#trx_addons_theme_panel_section_skins .trx_addons_image_block.skin_new .skin_label {
  background-color: #00a000;
}
#trx_addons_theme_panel_section_skins .trx_addons_image_block.skin_updated .skin_label {
  background-color: #a00000;
}

#trx_addons_theme_panel_section_skins.trx_addons_section_mode_thumbs .trx_addons_image_block_inner .trx_addons_image_block_image {
  height: 0;
  padding-top: 66.67%;
}
#trx_addons_theme_panel_section_skins.trx_addons_section_mode_thumbs .trx_addons_image_block_inner .trx_addons_image_block_title + .trx_addons_image_block_description {
  margin-top: 0.25em;
}

@media (min-width: 480px) {
  #trx_addons_theme_panel_section_skins.trx_addons_section_mode_list .trx_addons_image_block_wrap {
    -webkit-justify-content: space-between;
    -ms-flex-pack: space-between;
    justify-content: space-between;
  }
  #trx_addons_theme_panel_section_skins.trx_addons_section_mode_list .trx_addons_image_block {
    width: 48% !important;
  }
  #trx_addons_theme_panel_section_skins.trx_addons_section_mode_list .trx_addons_image_block_inner {
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
  }
  #trx_addons_theme_panel_section_skins.trx_addons_section_mode_list .trx_addons_image_block_image {
    width: 100%;
    height: 120px;
  }
  #trx_addons_theme_panel_section_skins.trx_addons_section_mode_list .trx_addons_image_block_footer {
    padding: 1em 0 0 0;
  }
  #trx_addons_theme_panel_section_skins.trx_addons_section_mode_list .trx_addons_image_block_footer .trx_addons_image_block_link {
    font-size: 14px;
  }
  #trx_addons_theme_panel_section_skins.trx_addons_section_mode_list .trx_addons_image_block_footer .trx_addons_image_block_description {
    margin-bottom: 0.25em;
  }
  #trx_addons_theme_panel_section_skins.trx_addons_section_mode_list .trx_addons_image_block_footer .trx_addons_image_block_link + .trx_addons_image_block_link {
    margin-top: 0;
  }
  #trx_addons_theme_panel_section_skins.trx_addons_section_mode_list .trx_addons_image_block_footer .trx_addons_image_block_link_delete_skin {
    margin-top: 0;
    order: 5;
    color: #b33836;
  }
  #trx_addons_theme_panel_section_skins.trx_addons_section_mode_list .trx_addons_image_block_footer .trx_addons_image_block_link_delete_skin:hover {
    color: #802826;
  }
}
@media (min-width: 768px) {
  #trx_addons_theme_panel_section_skins.trx_addons_section_mode_list .trx_addons_image_block_wrap {
    -webkit-justify-content: flex-start;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }
  #trx_addons_theme_panel_section_skins.trx_addons_section_mode_list .trx_addons_image_block {
    width: 50% !important;
    padding: 0 1em 1em 0;
  }
  #trx_addons_theme_panel_section_skins.trx_addons_section_mode_list .trx_addons_image_block_inner {
    -webkit-flex-direction: row;
    -ms-flex-direction: row;
    flex-direction: row;
  }
  #trx_addons_theme_panel_section_skins.trx_addons_section_mode_list .trx_addons_image_block_image {
    width: 130px;
    height: 110px;
  }
  #trx_addons_theme_panel_section_skins.trx_addons_section_mode_list .trx_addons_image_block_footer {
    padding: 0 0 0 1em;
  }
}
@media (min-width: 1280px) {
  #trx_addons_theme_panel_section_skins.trx_addons_section_mode_list .trx_addons_image_block {
    width: 33.3333% !important;
  }
}
@media (min-width: 1440px) {
  #trx_addons_theme_panel_section_skins.trx_addons_section_mode_list .trx_addons_image_block {
    width: 25% !important;
  }
}
@media (min-width: 1680px) {
  #trx_addons_theme_panel_section_skins.trx_addons_section_mode_list .trx_addons_image_block {
    width: 20% !important;
    padding: 0 1.5em 1.5em 0;
  }
  #trx_addons_theme_panel_section_skins.trx_addons_section_mode_list .trx_addons_image_block_footer {
    padding-left: 1em;
  }
}

#trx_addons_theme_panel_section_skins.trx_addons_section_mode_list .trx_addons_image_block_link_delete_skin span[data-tooltip-text], #trx_addons_theme_panel_section_skins.trx_addons_section_mode_thumbs .trx_addons_image_block_link_delete_skin .trx_addons_image_block_link_caption {
  display: none;
}

.elementra_upgrade_skins_button[disabled],
.elementra_upgrade_skins_button[disabled]:hover,
.elementra_upgrade_skins_button[disabled]:focus {
  color: #aaa;
  cursor: not-allowed;
}

.elementra_upgrade_skins_status_wrap {
  text-align: center;
  font-size: 1.5em;
}

.elementra_upgrade_skins_status:before {
  font-family: dashicons;
  display: inline-block;
  vertical-align: top;
}

.elementra_upgrade_skins_status_progress:before {
  content: '\f463';
  color: #11a0d2;
  -webkit-animation: spin 2s infinite linear;
  -ms-animation: spin 2s infinite linear;
  animation: spin 2s infinite linear;
}

.elementra_upgrade_skins_status_success:before {
  content: '\f147';
  color: #38bb7a;
}

.elementra_upgrade_skins_status_error:before {
  content: '\f335';
  color: #f04c49;
}

/* Change proportions of the skins image */
#update-skins-table .plugins .plugin-title img {
  width: 94px;
  height: 64px;
}

/*# sourceMappingURL=skins-admin.css.map */
