.single_style_style-1 .format-gallery .post_header_wrap .post_featured.with_thumb > img {
  -webkit-transform: scale(0.998, 0.998);
  -ms-transform: scale(0.998, 0.998);
  transform: scale(0.998, 0.998);
}
.single_style_style-1 .format-gallery .post_header_wrap .post_featured.with_thumb .slider_outer {
  position: absolute;
  z-index: 1000;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100% !important;
}
.single_style_style-1 .format-gallery .post_header_wrap .post_featured.with_thumb .slider_outer .slider_container {
  height: 100% !important;
}

.post_header_wrap_style_style-1 {
  position: relative;
  margin-bottom: 3em;
}
.post_header_wrap_style_style-1 .post_featured {
  margin: 0;
}
.post_header_wrap_style_style-1 .post_featured.post_featured_bg {
  height: 35rem;
}
.post_header_wrap_style_style-1 .post_featured.post_featured_bg:before {
  display: none;
}
.post_header_wrap_style_style-1 .post_featured img {
  max-width: 100%;
  width: auto;
  height: auto;
}
.post_header_wrap_style_style-1 .post_header {
  margin-bottom: 3em;
}

/*# sourceMappingURL=style-1.css.map */
