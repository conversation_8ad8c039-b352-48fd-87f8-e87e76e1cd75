Version: v.1.0.6
    Added:
        - TrxAddons: AI Helper - added support for the x.ai (Grok) API for generating texts and images
        - TrxAddons: WPML - added translation support for our Elementor Extensions like "Background Text"
        - TrxAddons: "Audio" widget updated with playlist and optional cover slider
        - Added support for the Tutor LMS plugin in skins where it is recommended
    Fixed:
        - SEO attributes now respect the "SEO snippets" option to avoid validation issues

Version: v.1.0.5
    Added:
        - Added new Expand/Collapse addon for toggling content visibility
        - Enabled the Excerpt length option when editing a page assigned as the Blog archive
        - New widget - Layouts: Login
        - Added support for the The Events Calendar and Event Tickets plugins in skins where they are recommended
    Fixed:
        - TrxAddons: Comprehensive updates and fixes across Elementor widgets, AI Helper, WPML compatibility, and layout components. Improvements include enhanced widget behavior, prompt generation, multilingual support, style adjustments, and better integration with Elementor and third-party plugins
        - TrxAddons: AI Helper – Updated and improved AI text generators for Elementor with refined prompt logic, JavaScript variable isolation, audio widget enhancements (hover styles, player width control), and compatibility fixes for icon rendering and download button behavior
        - Minor bug fixes and optimization
    Removed:
        - Removed 'medium' from external thumbnail sizes to prevent conflicts with the custom trx-addons-thumb-medium size

Version: v.1.0.4
    Added:
        - Added a forced check for addon and skin updates via the "Check again" link
        - In Give plugin support, added campaign pages (created with Gutenberg blocks) to the page detection function xxx_is_give_page()
        - AI Helper: Added the new "Ray 2.0 Flash" model from LumaLabs to the "Video Generator" widget
        - TrxAddons: Shortcode ICompare: Added customization settings – icon selection from Elementor's library and typography settings for before/after texts
        - TrxAddons: Elementor Widgets: Added a "Stretch Height" option to the Pricing Table widget to stick the footer with the button to the bottom of the block
    Fixed:
        - Replaced status_progress, status_success, and status_error icons during addon and skin updates with Dashicons
        - Elementor Templates: When importing a template, unavailable widgets are now replaced with an "Alert" widget showing a message about the missing widget (applies to widgets created by third-party plugins that are currently inactive)

Version: v.1.0.3
    Added:
        - Confirmed compatibility with WordPress 6.8
        - theme_filter_page_title_selector filters in JS and PHP to allow replacement of the page title selector in Elementor skins and clones
        - theme_filter_woocommerce_theme_support filter to customize WooCommerce arguments in Elementor skins and clones
        - Added support for the Give plugin in skins where it is recommended (Skin: Charity)
        - AI Helper: Added support for the new Stability AI API version (v2beta). two models from the old API version (v1) are still supported
        - AI Helper: Added a suffix with an instance number to form field IDs to prevent duplication and validation issues when multiple copies of the same shortcode are present on a page (no suffix is added to the first instance)
        - AI Helper: Replaced the <label> tag with <div> in the chat title to avoid validation errors caused by nested heading tags
        - AI Helper: Added a "Parse annotations" option to toggle the replacement of inline references (e.g., [1], [2]) in assistant responses with a corresponding list of sources below the reply. Enabled by default
    Fixed:
        - Moved loading of translation domains and theme plugins to priority -1 on the after_setup_theme action
        - Minor bug fixes and optimization

Version: v.1.0.2
    Added:
        - AI Helper: Removed the placeholder in all type="file" upload fields for W3C validator compatibility
        - Compatibility with Elementor's "Optimized Markup" experiment – when enabled, the 'common' section becomes 'common-optimized'. New parameters/sections now check for both naming variants
    Fixed:
        - Minor CSS fixes (input & checkbox | select2)

Version: v.1.0.1
    Added:
        - AI Helper: OpenAI-based trained AI Assistant V2
    Fixed:
        - Added selector to show separator before hidden option
        - AI Helper: styles of the AI widgets: Image Generator, Text Generator, AI Chat
    Removed:
        - .select_container wrapping for <select> – wrap manually if needed

Release: v.1.0.0      