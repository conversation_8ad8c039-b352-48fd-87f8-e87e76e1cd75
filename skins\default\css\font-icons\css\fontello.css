@font-face {
  font-family: 'fontello';
  src: url('../font/fontello.eot?87124065');
  src: url('../font/fontello.eot?87124065#iefix') format('embedded-opentype'),
       url('../font/fontello.woff2?87124065') format('woff2'),
       url('../font/fontello.woff?87124065') format('woff'),
       url('../font/fontello.ttf?87124065') format('truetype'),
       url('../font/fontello.svg?87124065#fontello') format('svg');
  font-weight: normal;
  font-style: normal;
}
/* Chrome hack: SVG is rendered more smooth in Windozze. 100% magic, uncomment if you need it. */
/* Note, that will break hinting! In other OS-es font will be not as sharp as it could be */
/*
@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: 'fontello';
    src: url('../font/fontello.svg?87124065#fontello') format('svg');
  }
}
*/
[class^="icon-"]:before, [class*=" icon-"]:before {
  font-family: "fontello";
  font-style: normal;
  font-weight: normal;
  speak: never;

  display: inline-block;
  text-decoration: inherit;
  width: 1em;
  margin-right: .2em;
  text-align: center;
  /* opacity: .8; */

  /* For safety - reset parent styles, that can break glyph codes*/
  font-variant: normal;
  text-transform: none;

  /* fix buttons height, for twitter bootstrap */
  line-height: 1em;

  /* Animation center compensation - margins should be symmetric */
  /* remove if not needed */
  margin-left: .2em;

  /* you can be more comfortable with increased icons size */
  /* font-size: 120%; */

  /* Font smoothing. That was taken from TWBS */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* Uncomment for 3D effect */
  /* text-shadow: 1px 1px 1px rgba(127, 127, 127, 0.3); */
}

.icon-heart-empty:before { content: '\e800'; } /* '' */
.icon-whatsapp:before { content: '\e801'; } /* '' */
.icon-threads:before { content: '\e802'; } /* '' */
.icon-load-arrow:before { content: '\e803'; } /* '' */
.icon-checkbox:before { content: '\e804'; } /* '' */
.icon-checkbox-active:before { content: '\e805'; } /* '' */
.icon-ccw:before { content: '\e80e'; } /* '' */
.icon-mobile:before { content: '\e822'; } /* '' */
.icon-tablet-1:before { content: '\e823'; } /* '' */
.icon-laptop:before { content: '\e824'; } /* '' */
.icon-desktop:before { content: '\e825'; } /* '' */
.icon-heart-full:before { content: '\e827'; } /* '' */
.icon-down:before { content: '\e828'; } /* '' */
.icon-email:before { content: '\e829'; } /* '' */
.icon-link:before { content: '\e82a'; } /* '' */
.icon-spin3:before { content: '\e82d'; } /* '' */
.icon-up:before { content: '\e835'; } /* '' */
.icon-right:before { content: '\e836'; } /* '' */
.icon-left:before { content: '\e837'; } /* '' */
.icon-search:before { content: '\e83a'; } /* '' */
.icon-twitter:before { content: '\e83b'; } /* '' */
.icon-dot:before { content: '\e83c'; } /* '' */
.icon-youtube:before { content: '\e83d'; } /* '' */
.icon-discord:before { content: '\e83f'; } /* '' */
.icon-dribbble:before { content: '\e840'; } /* '' */
.icon-facebook:before { content: '\e841'; } /* '' */
.icon-instagram:before { content: '\e842'; } /* '' */
.icon-linkedin:before { content: '\e843'; } /* '' */
.icon-pinterest:before { content: '\e844'; } /* '' */
.icon-reddit:before { content: '\e845'; } /* '' */
.icon-telegram:before { content: '\e846'; } /* '' */
.icon-comment-light:before { content: '\e847'; } /* '' */
.icon-tumblr:before { content: '\e849'; } /* '' */
.icon-tiktok:before { content: '\e84a'; } /* '' */
.icon-behance:before { content: '\e84b'; } /* '' */
.icon-check:before { content: '\e8ab'; } /* '' */
.icon-plus:before { content: '\e8ad'; } /* '' */
.icon-menu:before { content: '\e8ba'; } /* '' */
.icon-pencil:before { content: '\e8bb'; } /* '' */
.icon-play:before { content: '\e8e1'; } /* '' */
.icon-clear-button:before { content: '\e916'; } /* '' */
.icon-menu-2:before { content: '\e93c'; } /* '' */
.icon-download:before { content: '\e959'; } /* '' */
.icon-upload:before { content: '\e95a'; } /* '' */
.icon-font:before { content: '\e95d'; } /* '' */
.icon-home-2:before { content: '\e95e'; } /* '' */
.icon-plugins:before { content: '\e960'; } /* '' */
.icon-settings:before { content: '\e961'; } /* '' */
.icon-smartphone:before { content: '\e962'; } /* '' */
.icon-check-2:before { content: '\e963'; } /* '' */
.icon-customizer:before { content: '\e964'; } /* '' */
.icon-editor-table:before { content: '\e965'; } /* '' */
.icon-footer:before { content: '\e966'; } /* '' */
.icon-header:before { content: '\e967'; } /* '' */
.icon-plus-2:before { content: '\e968'; } /* '' */
.icon-cart-2:before { content: '\e96a'; } /* '' */
.icon-star-filled:before { content: '\e96e'; } /* '' */
.icon-tag-2:before { content: '\e96f'; } /* '' */
.icon-building:before { content: '\e971'; } /* '' */
.icon-format-image:before { content: '\e973'; } /* '' */
.icon-portfolio:before { content: '\e976'; } /* '' */
.icon-screenoptions:before { content: '\e977'; } /* '' */
.icon-padlock:before { content: '\e97b'; } /* '' */
.icon-padlock-unlock:before { content: '\e97c'; } /* '' */
.icon-down-open:before { content: '\f004'; } /* '' */
.icon-up-open:before { content: '\f005'; } /* '' */
.icon-right-open:before { content: '\f006'; } /* '' */
.icon-left-open:before { content: '\f007'; } /* '' */
.icon-cars:before { content: '\f502'; } /* '' */
.icon-category:before { content: '\f503'; } /* '' */
.icon-certificates:before { content: '\f504'; } /* '' */
.icon-courses:before { content: '\f505'; } /* '' */
.icon-dishes:before { content: '\f506'; } /* '' */
.icon-portfolio-1:before { content: '\f507'; } /* '' */
.icon-posts-page:before { content: '\f508'; } /* '' */
.icon-resume:before { content: '\f509'; } /* '' */
.icon-search-1:before { content: '\f50a'; } /* '' */
.icon-services:before { content: '\f50b'; } /* '' */
.icon-sport:before { content: '\f50c'; } /* '' */
.icon-team:before { content: '\f50d'; } /* '' */
.icon-testimonials:before { content: '\f50e'; } /* '' */
.icon-additional-css:before { content: '\f50f'; } /* '' */
.icon-homepage-settings:before { content: '\f510'; } /* '' */
.icon-blog:before { content: '\f511'; } /* '' */
.icon-single-post:before { content: '\f512'; } /* '' */
.icon-tag-1:before { content: '\f513'; } /* '' */
