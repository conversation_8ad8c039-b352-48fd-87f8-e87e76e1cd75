@charset "UTF-8";
/* TABLE OF CONTENTS

1. Theme-specific variables and classes
2. Default tag's settings
3. Form fields settings
4. WP styles and Screen readers
5. Theme grid
6. Page layouts
7. Section's decorations
   7.1 Header: <PERSON><PERSON> and <PERSON>u
   7.2 Post info (page/post title, category or tag name, author, meta, etc.)
   7.3 Post Formats
   7.4 Paginations
8. General pages
   8.1 Page 404
   8.2 Page 'No search results' and 'No archive results'
   8.3 Author's page
9. Sidebars
10. Footer areas
11. Utils
12. Third part plugins
13. User utilities
-------------------------------------------------------------- */
/* 1. Theme-specific variables and classes
-------------------------------------------------------------- */
:root {
  --theme-var-koef_narrow: 0.75;
  --theme-var-page: var(--theme-var-page_width);
  --theme-var-sidebar_width_max: 500px;
  --theme-var-sidebar_width_min: 150px;
  --theme-var-sidebar_gap_width_max: 100px;
  --theme-var-sidebar_gap_width_min: 0px;
  --theme-var-sidebar: clamp( var(--theme-var-sidebar_width_min, 150px), calc( var(--theme-var-page) * var(--theme-var-sidebar_prc) * var(--theme-var-sidebar_proportional) + var(--theme-var-sidebar_width) * ( 1 - var(--theme-var-sidebar_proportional) ) ), var(--theme-var-sidebar_width_max, 500px) );
  --theme-var-sidebar_gap: clamp( var(--theme-var-sidebar_gap_width_min, 0px), calc( var(--theme-var-page) * var(--theme-var-sidebar_gap_prc) * var(--theme-var-sidebar_proportional) + var(--theme-var-sidebar_gap_width) * ( 1 - var(--theme-var-sidebar_proportional) ) ), var(--theme-var-sidebar_gap_width_max, 100px) );
  --theme-var-sidebar_and_gap: calc( var(--theme-var-sidebar) + var(--theme-var-sidebar_gap) );
  --theme-var-content: calc( var(--theme-var-page) - var(--theme-var-sidebar) - var(--theme-var-sidebar_gap) );
  --theme-var-content_narrow: calc( var(--theme-var-content) * var(--theme-var-koef_narrow) );
  --theme-var-padding_narrow: calc( var(--theme-var-content) * ( 1 - var(--theme-var-koef_narrow) ) );
  --fixed-rows-height: 0px;
  --theme-var-main_content_padding: 9rem;
  --theme-var-single_post_block_margin: 5.8rem;
}

.body_style_boxed {
  --theme-var-page_boxed: calc( var(--theme-var-page_width) + var(--theme-var-page_boxed_extra) * 2 );
  --theme-var-page: calc( var(--theme-var-page_boxed) - var(--theme-var-page_boxed_extra) * 2 );
  --theme-var-sidebar: clamp( var(--theme-var-sidebar_width_min, 150px), calc( var(--theme-var-page) * var(--theme-var-sidebar_prc) * var(--theme-var-sidebar_proportional) + var(--theme-var-sidebar_width) * ( 1 - var(--theme-var-sidebar_proportional) ) ), var(--theme-var-sidebar_width_max, 500px) );
  --theme-var-sidebar_gap: clamp( var(--theme-var-sidebar_gap_width_min, 0px), calc( var(--theme-var-page) * var(--theme-var-sidebar_gap_prc) * var(--theme-var-sidebar_proportional) + var(--theme-var-sidebar_gap_width) * ( 1 - var(--theme-var-sidebar_proportional) ) ), var(--theme-var-sidebar_gap_width_max, 100px) );
  --theme-var-sidebar_and_gap: calc( var(--theme-var-sidebar) + var(--theme-var-sidebar_gap) );
  --theme-var-content: calc( var(--theme-var-page) - var(--theme-var-sidebar) - var(--theme-var-sidebar_gap) );
  --theme-var-content_narrow: calc( var(--theme-var-content) * var(--theme-var-koef_narrow) );
  --theme-var-padding_narrow: calc( var(--theme-var-content) * ( 1 - var(--theme-var-koef_narrow) ) );
}

/* 
To ensure that in the “fullwide” mode, the custom (layouts) footer does not stretch like content, but remains in the regular grid. If necessary, the footer can be configured in the builder.
This does not apply to the default footer, it will stretch.
.body_style_fullwide {
	--theme-var-page: calc( 100vw - var(--theme-var-page_fullwide_extra) * 2 );	// Page width
	@ -del-space- include page_dimensions;
}
*/
/* 2. Default tag's settings
-------------------------------------------------------------- */
html {
  font-size: 16px;
}

body {
  color: var(--theme-color-text);
  background-color: var(--theme-color-bg_color);
  scrollbar-color: var(--theme-color-bd_color) var(--theme-color-bg_color_2);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
body::-webkit-scrollbar {
  width: 16px;
}
body::-webkit-scrollbar-track {
  background: var(--theme-color-bg_color_2);
}
body::-webkit-scrollbar-thumb {
  background-color: var(--theme-color-bd_color);
  border: 2px solid var(--theme-color-bg_color_2);
}

body.body_style_boxed .page_wrap {
  background-color: var(--theme-color-bg_color);
}

article, aside, details, footer, header, hgroup, nav, section {
  display: block;
}

/* Lists */
li > p + p {
  margin-top: 0.5em;
}

ol, ul {
  padding-left: 1.2em;
}

li > ol, li > ul, li > dl, dl > dl {
  margin-bottom: 0 !important;
}

li > p {
  margin-bottom: 0;
}

li a {
  color: var(--theme-color-title);
}
li a:hover {
  color: var(--theme-color-link);
}

ul > li:before {
  color: var(--theme-color-link);
}

/* Links */
a {
  text-decoration: none;
  background: transparent;
  color: var(--theme-color-link);
}

a:hover {
  color: var(--theme-color-hover);
}

a,
a:hover,
:focus, a:focus,
:active, a:active {
  outline: 0;
}

:where([style*="text-decoration:"]) > a {
  text-decoration: inherit;
}

body.show_outline :focus, body.show_outline a:focus {
  outline: thin dotted !important;
}

a[href="javascript:void(0)"] {
  cursor: default;
}

a img {
  border: none;
}

a,
button,
input[type="button"],
input[type="submit"] {
  -webkit-transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
  -ms-transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
}

h1, h1 a, h2, h2 a, h3, h3 a, h4, h4 a, h5, h5 a, h6, h6 a {
  color: var(--theme-color-title);
}
h1 a:hover, h2 a:hover, h3 a:hover, h4 a:hover, h5 a:hover, h6 a:hover {
  color: var(--theme-color-link);
}
h1:first-child, h2:first-child, h3:first-child, h4:first-child, h5:first-child, h6:first-child {
  margin-top: 0;
}
h1 > a, h2 > a, h3 > a, h4 > a, h5 > a, h6 > a {
  display: block;
}

/* Tables */
html :where([style*="border-width"]) {
  border-style: solid;
}

.wp-block-table td, .wp-block-table th, table td, table th {
  border: none;
  border-color: var(--theme-color-bd_color);
  padding: 1rem;
}

table {
  border-collapse: collapse;
  width: 100%;
}
table > p {
  margin: 0 !important;
}
table th {
  color: var(--theme-color-alt_title);
  background-color: var(--theme-color-alt_bg_color);
}
table th b, table th strong {
  color: var(--theme-color-alt_title);
}
table th a {
  color: var(--theme-color-alt_link);
}
table th a:hover {
  color: var(--theme-color-alt_hover);
}
table caption {
  margin-bottom: 0.5em;
  text-align: center;
}
table > tbody > tr:nth-child(2n+1) > td {
  background-color: var(--theme-color-bg_color_2);
}
table > tbody > tr:nth-child(2n) > td {
  background-color: var(--theme-color-bg_color);
}

/* Blockquotes */
blockquote {
  position: relative;
  text-align: left;
  overflow: hidden;
}

blockquote,
.wp-block-pullquote {
  -webkit-border-radius: var(--theme-var-global-border-radius, 0);
  -ms-border-radius: var(--theme-var-global-border-radius, 0);
  border-radius: var(--theme-var-global-border-radius, 0);
}

.wp-block-pullquote > blockquote {
  -webkit-border-radius: inherit;
  -ms-border-radius: inherit;
  border-radius: inherit;
}

blockquote,
blockquote[class*="wp-block-quote"][class*="is-style-"],
blockquote[class*="wp-block-quote"][class*="is-"],
.wp-block-quote:not(.is-large):not(.is-style-large),
.wp-block-freeform.block-library-rich-text__tinymce blockquote {
  -webkit-box-sizing: border-box;
  -ms-box-sizing: border-box;
  box-sizing: border-box;
}
blockquote:not(.is-style-plain),
blockquote[class*="wp-block-quote"][class*="is-style-"]:not(.is-style-plain),
blockquote[class*="wp-block-quote"][class*="is-"]:not(.is-style-plain),
.wp-block-quote:not(.is-large):not(.is-style-large):not(.is-style-plain),
.wp-block-freeform.block-library-rich-text__tinymce blockquote:not(.is-style-plain) {
  padding: 3.2em 2.6em;
}
blockquote.is-style-plain,
blockquote[class*="wp-block-quote"][class*="is-style-"].is-style-plain,
blockquote[class*="wp-block-quote"][class*="is-"].is-style-plain,
.wp-block-quote:not(.is-large):not(.is-style-large).is-style-plain,
.wp-block-freeform.block-library-rich-text__tinymce blockquote.is-style-plain {
  padding: 1em;
}

blockquote:not(.is-style-plain)[class*="wp-block-quote"][class*="is-style-large"],
blockquote:not(.is-style-plain)[class*="wp-block-quote"][class*="is-large"] {
  margin-top: 0;
  margin-bottom: 2em;
}

.wp-block-pullquote > blockquote,
.wp-block-column blockquote {
  margin: 0 !important;
  max-width: none !important;
}

.blog_mode_post blockquote:not(.is-style-plain),
.blog_mode_page blockquote:not(.is-style-plain) {
  margin: var(--theme-font-p_margin-bottom) 0;
}
.blog_mode_post .comments_list blockquote:not(.is-style-plain),
.blog_mode_page .comments_list blockquote:not(.is-style-plain) {
  margin: var(--theme-font-p_margin-bottom) 0;
}

blockquote p,
.wp-block-quote p {
  margin: 0;
}
blockquote p + p,
.wp-block-quote p + p {
  margin-top: 0.5em;
}

blockquote > cite, blockquote > p > cite,
blockquote > .wp-block-pullquote__citation,
.wp-block-quote .wp-block-quote__citation {
  display: block;
  margin-top: 1.2em;
}

blockquote .block-library-pullquote__content {
  margin-bottom: 2.5em;
}

.wp-block-pullquote {
  padding: 0;
  border-width: 0;
}

.wp-block-pullquote[class*="align"] blockquote {
  margin-left: 0;
  margin-right: 0;
}

blockquote .wp-block-pullquote__citation {
  margin-top: 0;
}

section > blockquote:not(.has-background):not(.is-style-plain),
div:not(.has-background) > blockquote:not(.has-background):not(.is-style-plain),
figure:not(.has-background) > blockquote:not(.has-background):not(.is-style-plain) {
  background-color: var(--theme-color-bg_color_2);
}

section > blockquote:not(.has-text-color):not(.is-style-plain), section > blockquote:not(.has-text-color):not(.is-style-plain) p,
div:not(.has-text-color) > blockquote:not(.has-text-color):not(.is-style-plain),
div:not(.has-text-color) > blockquote:not(.has-text-color):not(.is-style-plain) p,
figure:not(.has-text-color) > blockquote:not(.has-text-color):not(.is-style-plain),
figure:not(.has-text-color) > blockquote:not(.has-text-color):not(.is-style-plain) p {
  color: var(--theme-color-title) !important;
}
section > blockquote:not(.has-text-color):not(.is-style-plain) a,
div:not(.has-text-color) > blockquote:not(.has-text-color):not(.is-style-plain) a,
figure:not(.has-text-color) > blockquote:not(.has-text-color):not(.is-style-plain) a {
  color: var(--theme-color-link);
}
section > blockquote:not(.has-text-color):not(.is-style-plain) a:hover,
div:not(.has-text-color) > blockquote:not(.has-text-color):not(.is-style-plain) a:hover,
figure:not(.has-text-color) > blockquote:not(.has-text-color):not(.is-style-plain) a:hover {
  color: var(--theme-color-hover);
}
section > blockquote:not(.has-text-color):not(.is-style-plain) dt, section > blockquote:not(.has-text-color):not(.is-style-plain) b, section > blockquote:not(.has-text-color):not(.is-style-plain) strong, section > blockquote:not(.has-text-color):not(.is-style-plain) i, section > blockquote:not(.has-text-color):not(.is-style-plain) em, section > blockquote:not(.has-text-color):not(.is-style-plain) mark, section > blockquote:not(.has-text-color):not(.is-style-plain) ins,
div:not(.has-text-color) > blockquote:not(.has-text-color):not(.is-style-plain) dt,
div:not(.has-text-color) > blockquote:not(.has-text-color):not(.is-style-plain) b,
div:not(.has-text-color) > blockquote:not(.has-text-color):not(.is-style-plain) strong,
div:not(.has-text-color) > blockquote:not(.has-text-color):not(.is-style-plain) i,
div:not(.has-text-color) > blockquote:not(.has-text-color):not(.is-style-plain) em,
div:not(.has-text-color) > blockquote:not(.has-text-color):not(.is-style-plain) mark,
div:not(.has-text-color) > blockquote:not(.has-text-color):not(.is-style-plain) ins,
figure:not(.has-text-color) > blockquote:not(.has-text-color):not(.is-style-plain) dt,
figure:not(.has-text-color) > blockquote:not(.has-text-color):not(.is-style-plain) b,
figure:not(.has-text-color) > blockquote:not(.has-text-color):not(.is-style-plain) strong,
figure:not(.has-text-color) > blockquote:not(.has-text-color):not(.is-style-plain) i,
figure:not(.has-text-color) > blockquote:not(.has-text-color):not(.is-style-plain) em,
figure:not(.has-text-color) > blockquote:not(.has-text-color):not(.is-style-plain) mark,
figure:not(.has-text-color) > blockquote:not(.has-text-color):not(.is-style-plain) ins {
  color: var(--theme-color-title);
}
section > blockquote:not(.has-text-color):not(.is-style-plain) s, section > blockquote:not(.has-text-color):not(.is-style-plain) strike, section > blockquote:not(.has-text-color):not(.is-style-plain) del,
div:not(.has-text-color) > blockquote:not(.has-text-color):not(.is-style-plain) s,
div:not(.has-text-color) > blockquote:not(.has-text-color):not(.is-style-plain) strike,
div:not(.has-text-color) > blockquote:not(.has-text-color):not(.is-style-plain) del,
figure:not(.has-text-color) > blockquote:not(.has-text-color):not(.is-style-plain) s,
figure:not(.has-text-color) > blockquote:not(.has-text-color):not(.is-style-plain) strike,
figure:not(.has-text-color) > blockquote:not(.has-text-color):not(.is-style-plain) del {
  color: var(--theme-color-meta);
}
section > blockquote:not(.has-text-color):not(.is-style-plain) code,
div:not(.has-text-color) > blockquote:not(.has-text-color):not(.is-style-plain) code,
figure:not(.has-text-color) > blockquote:not(.has-text-color):not(.is-style-plain) code {
  color: var(--theme-color-title);
  background-color: var(--theme-color-bg_color);
  border-color: var(--theme-color-bd_color);
}

/* Dropcaps */
.has-drop-cap:not(:focus):first-letter {
  padding: 0;
  margin: 0.1em 0.25em -0.1em 0;
  color: var(--theme-color-title);
}

/* Other tags */
dd {
  margin-left: 1.5em;
}

dt, b, strong {
  font-weight: bold;
}

dfn, em, i {
  font-style: italic;
}

pre, code, kbd, tt, var, samp {
  font-family: "Courier New", Courier, monospace;
  font-size: 1em;
  letter-spacing: 0;
}

pre {
  overflow: auto;
  max-width: 100%;
  white-space: pre-wrap;
}

code {
  overflow: auto;
  max-width: 100%;
  padding: 0 1em;
  display: inline-block;
  vertical-align: middle;
  word-wrap: break-word;
  color: var(--theme-color-text);
  background-color: var(--theme-color-bg_color_2);
  border: 1px solid var(--theme-color-bd_color);
}

pre > code {
  display: block;
  padding: 0.7em 1em;
}

abbr, acronym {
  border-bottom: 1px dotted;
  cursor: help;
}

mark, ins {
  background-color: transparent;
  text-decoration: none;
}

sup, sub {
  font-size: 75%;
  height: 0;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sup {
  bottom: 1ex;
}

sub {
  top: .5ex;
}

small {
  font-size: 80%;
}

big {
  font-size: 120%;
}

[hidden], template {
  display: none;
}

hr {
  -webkit-box-sizing: content-box;
  -ms-box-sizing: content-box;
  box-sizing: content-box;
  height: 0;
  border: none;
  border-top: 1px solid var(--theme-color-bd_color);
  margin-top: calc(var(--theme-var-main_content_padding) / 2) !important;
  margin-bottom: calc(var(--theme-var-main_content_padding) / 2) !important;
}

.wp-block-separator:not([class*="is-style"]) {
  width: 15%;
}

/* Fontello icons */
[class^="icon-"]:before,
[class*=" icon-"]:before {
  font-size: inherit;
  line-height: inherit !important;
  font-weight: inherit;
  font-style: inherit;
  display: inline-block;
  width: auto;
  margin: 0;
}

/* Images */
img {
  max-width: 100%;
  height: auto;
  /* Only height: auto; not both! */
  vertical-align: top;
}

.wp-block-gallery {
  margin-top: 0;
  margin-bottom: 1.4em;
}

.wp-block-gallery .blocks-gallery-image figure,
.wp-block-gallery .blocks-gallery-item figure {
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-start;
  -ms-flex-align: start;
  align-items: flex-start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

/* Fix for WordPress 5.9+ */
/*.wp-block-gallery.has-nested-images,*/
figure.wp-block-gallery {
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  -webkit-align-items: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
}

figure,
.wp-caption,
.wp-caption-overlay .wp-caption {
  border: 0;
  margin: 0;
  padding: 0;
  overflow: hidden;
  position: relative;
  max-width: 100%;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

figure,
.wp-caption {
  margin-bottom: 1em;
}

p figure,
p .wp-caption {
  margin-bottom: 0;
}

figure figcaption,
.wp-block-image figcaption,
.wp-block-audio figcaption,
.wp-block-video figcaption,
.wp-block-embed figcaption,
.wp-block-gallery .blocks-gallery-image figcaption,
.wp-block-gallery .blocks-gallery-item figcaption,
.wp-block-gallery.has-nested-images figure.wp-block-image figcaption,
.wp-block-gallery:not(.has-nested-images) .blocks-gallery-item figcaption,
.blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-item figcaption,
.wp-caption .wp-caption-text,
.wp-caption .wp-caption-dd,
.wp-caption-overlay .wp-caption .wp-caption-text,
.wp-caption-overlay .wp-caption .wp-caption-dd {
  font-size: 0.9375em;
  line-height: var(--theme-font-p_line-height);
  font-weight: var(--theme-font-p_font-weight);
  font-style: var(--theme-font-p_font-style);
  -webkit-box-sizing: border-box;
  -ms-box-sizing: border-box;
  box-sizing: border-box;
  position: relative;
  top: auto;
  bottom: auto;
  left: auto;
  right: auto;
  display: block;
  margin: 0;
  text-align: left;
  padding: 10px 0 0 !important;
  color: var(--theme-color-meta);
  background: none;
  max-height: 6em;
  overflow-x: hidden;
  overflow-y: auto;
  scrollbar-gutter: stable;
  -webkit-flex-grow: 0;
  -ms-flex-grow: 0;
  flex-grow: 0;
  -webkit-flex-basis: auto;
  -ms-flex-basis: auto;
  flex-basis: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--theme-color-bd_color) var(--theme-color-bg_color_2);
}
figure figcaption::-webkit-scrollbar,
.wp-block-image figcaption::-webkit-scrollbar,
.wp-block-audio figcaption::-webkit-scrollbar,
.wp-block-video figcaption::-webkit-scrollbar,
.wp-block-embed figcaption::-webkit-scrollbar,
.wp-block-gallery .blocks-gallery-image figcaption::-webkit-scrollbar,
.wp-block-gallery .blocks-gallery-item figcaption::-webkit-scrollbar,
.wp-block-gallery.has-nested-images figure.wp-block-image figcaption::-webkit-scrollbar,
.wp-block-gallery:not(.has-nested-images) .blocks-gallery-item figcaption::-webkit-scrollbar,
.blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-item figcaption::-webkit-scrollbar,
.wp-caption .wp-caption-text::-webkit-scrollbar,
.wp-caption .wp-caption-dd::-webkit-scrollbar,
.wp-caption-overlay .wp-caption .wp-caption-text::-webkit-scrollbar,
.wp-caption-overlay .wp-caption .wp-caption-dd::-webkit-scrollbar {
  width: 6px;
}
figure figcaption::-webkit-scrollbar-track,
.wp-block-image figcaption::-webkit-scrollbar-track,
.wp-block-audio figcaption::-webkit-scrollbar-track,
.wp-block-video figcaption::-webkit-scrollbar-track,
.wp-block-embed figcaption::-webkit-scrollbar-track,
.wp-block-gallery .blocks-gallery-image figcaption::-webkit-scrollbar-track,
.wp-block-gallery .blocks-gallery-item figcaption::-webkit-scrollbar-track,
.wp-block-gallery.has-nested-images figure.wp-block-image figcaption::-webkit-scrollbar-track,
.wp-block-gallery:not(.has-nested-images) .blocks-gallery-item figcaption::-webkit-scrollbar-track,
.blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-item figcaption::-webkit-scrollbar-track,
.wp-caption .wp-caption-text::-webkit-scrollbar-track,
.wp-caption .wp-caption-dd::-webkit-scrollbar-track,
.wp-caption-overlay .wp-caption .wp-caption-text::-webkit-scrollbar-track,
.wp-caption-overlay .wp-caption .wp-caption-dd::-webkit-scrollbar-track {
  background: var(--theme-color-bg_color_2);
}
figure figcaption::-webkit-scrollbar-thumb,
.wp-block-image figcaption::-webkit-scrollbar-thumb,
.wp-block-audio figcaption::-webkit-scrollbar-thumb,
.wp-block-video figcaption::-webkit-scrollbar-thumb,
.wp-block-embed figcaption::-webkit-scrollbar-thumb,
.wp-block-gallery .blocks-gallery-image figcaption::-webkit-scrollbar-thumb,
.wp-block-gallery .blocks-gallery-item figcaption::-webkit-scrollbar-thumb,
.wp-block-gallery.has-nested-images figure.wp-block-image figcaption::-webkit-scrollbar-thumb,
.wp-block-gallery:not(.has-nested-images) .blocks-gallery-item figcaption::-webkit-scrollbar-thumb,
.blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-item figcaption::-webkit-scrollbar-thumb,
.wp-caption .wp-caption-text::-webkit-scrollbar-thumb,
.wp-caption .wp-caption-dd::-webkit-scrollbar-thumb,
.wp-caption-overlay .wp-caption .wp-caption-text::-webkit-scrollbar-thumb,
.wp-caption-overlay .wp-caption .wp-caption-dd::-webkit-scrollbar-thumb {
  background-color: var(--theme-color-bd_color);
  border: 1px solid var(--theme-color-bg_color_2);
  -webkit-border-radius: 6px;
  -ms-border-radius: 6px;
  border-radius: 6px;
}
figure figcaption a,
.wp-block-image figcaption a,
.wp-block-audio figcaption a,
.wp-block-video figcaption a,
.wp-block-embed figcaption a,
.wp-block-gallery .blocks-gallery-image figcaption a,
.wp-block-gallery .blocks-gallery-item figcaption a,
.wp-block-gallery.has-nested-images figure.wp-block-image figcaption a,
.wp-block-gallery:not(.has-nested-images) .blocks-gallery-item figcaption a,
.blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-item figcaption a,
.wp-caption .wp-caption-text a,
.wp-caption .wp-caption-dd a,
.wp-caption-overlay .wp-caption .wp-caption-text a,
.wp-caption-overlay .wp-caption .wp-caption-dd a {
  color: var(--theme-color-title);
}
figure figcaption a:hover,
.wp-block-image figcaption a:hover,
.wp-block-audio figcaption a:hover,
.wp-block-video figcaption a:hover,
.wp-block-embed figcaption a:hover,
.wp-block-gallery .blocks-gallery-image figcaption a:hover,
.wp-block-gallery .blocks-gallery-item figcaption a:hover,
.wp-block-gallery.has-nested-images figure.wp-block-image figcaption a:hover,
.wp-block-gallery:not(.has-nested-images) .blocks-gallery-item figcaption a:hover,
.blocks-gallery-grid:not(.has-nested-images) .blocks-gallery-item figcaption a:hover,
.wp-caption .wp-caption-text a:hover,
.wp-caption .wp-caption-dd a:hover,
.wp-caption-overlay .wp-caption .wp-caption-text a:hover,
.wp-caption-overlay .wp-caption .wp-caption-dd a:hover {
  color: var(--theme-color-link);
}

.wp-block-gallery.has-nested-images figure.wp-block-image figcaption:hover {
  scrollbar-width: thin;
  scrollbar-color: var(--theme-color-bd_color) var(--theme-color-bg_color_2);
}
.wp-block-gallery.has-nested-images figure.wp-block-image figcaption:hover::-webkit-scrollbar {
  width: 8px;
}
.wp-block-gallery.has-nested-images figure.wp-block-image figcaption:hover::-webkit-scrollbar-track {
  background: var(--theme-color-bg_color_2);
}
.wp-block-gallery.has-nested-images figure.wp-block-image figcaption:hover::-webkit-scrollbar-thumb {
  background-color: var(--theme-color-bd_color);
  border: 1px solid var(--theme-color-bg_color_2);
  -webkit-border-radius: 6px;
  -ms-border-radius: 6px;
  border-radius: 6px;
}

.wp-block-image .alignleft figcaption, img.alignleft figcaption,
.wp-block-image .alignright figcaption, img.alignright figcaption,
.wp-block-image .aligncenter figcaption, img.aligncenter figcaption,
.wp-block-image.is-resized figcaption {
  display: block;
  color: var(--theme-color-meta) !important;
}

.wp-block-freeform.block-library-rich-text__tinymce dd.wp-caption-dd a {
  display: inline;
}

svg:not(:root) {
  overflow: hidden;
}

/* Gallery (old) */
.gallery {
  margin: 0 -5px;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-align-items: flex-start;
  -ms-flex-align: start;
  align-items: flex-start;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.gallery-item {
  overflow: hidden;
  -webkit-box-sizing: border-box;
  -ms-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0 5px;
}

figure.gallery-item {
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}

.gallery-columns-9 .gallery-item {
  width: 11.1111111111% !important;
}

.gallery-columns-8 .gallery-item {
  width: 12.5% !important;
}

.gallery-columns-7 .gallery-item {
  width: 14.2857142857% !important;
}

.gallery-columns-6 .gallery-item {
  width: 16.6666666667% !important;
}

.gallery-columns-5 .gallery-item {
  width: 20% !important;
}

.gallery-columns-4 .gallery-item {
  width: 25% !important;
}

.gallery-columns-3 .gallery-item {
  width: 33.3333333333% !important;
}

.gallery-columns-2 .gallery-item {
  width: 50% !important;
}

.gallery-columns-1 .gallery-item {
  width: 100% !important;
}

.gallery-item a {
  display: block;
}

.gallery-item a img {
  border: none;
  display: block;
  width: 100%;
}

.gallery-columns-9 .gallery-caption, .gallery-columns-8 .gallery-caption, .gallery-columns-7 .gallery-caption, .gallery-columns-6 .gallery-caption {
  font-size: 0.875em;
  line-height: 1.5em;
}

/* Audio and Video */
audio,
canvas,
progress,
video {
  display: inline-block;
  vertical-align: baseline;
}

video {
  width: 100%;
  height: auto;
}

video.wp-block-cover__video-background {
  width: 100% !important;
  height: 100% !important;
}

audio:not([controls]) {
  display: none;
  height: 0;
}

iframe, video, embed {
  max-width: 100%;
  min-height: 100px;
  vertical-align: top;
}

.wp-block-embed.alignwide iframe,
.wp-block-embed.alignfull iframe {
  width: 100%;
}

figure.wp-block-audio,
figure.wp-block-video,
figure.wp-block-embed {
  overflow: visible;
}

figure.wp-block-audio {
  display: block;
}

figure.wp-block-audio figcaption,
figure.wp-block-video figcaption,
figure.wp-block-embed figcaption {
  margin: 1em 0 0 !important;
  padding: 0 !important;
}

/* Embed blocks */
.wp-block-embed.wp-has-aspect-ratio {
  display: block;
}

.wp-block-embed .wp-block-embed__wrapper {
  position: relative;
  max-width: 100%;
}

.wp-block-embed.alignwide .wp-block-embed__wrapper iframe,
.wp-block-embed.alignfull .wp-block-embed__wrapper iframe,
.wp-block-embed[class*="wp-embed-aspect-"] .wp-block-embed__wrapper iframe {
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
}

.wp-block-embed.is-type-video.alignwide .wp-block-embed__wrapper:before,
.wp-block-embed.is-type-video.alignfull .wp-block-embed__wrapper:before,
.wp-block-embed.is-type-video[class*="wp-embed-aspect-"] .wp-block-embed__wrapper:before {
  content: "";
  display: block;
  width: 0;
}

.wp-block-embed.is-type-video.alignwide .wp-block-embed__wrapper:before,
.wp-block-embed.is-type-video.alignfull .wp-block-embed__wrapper:before,
.wp-block-embed.is-type-video.wp-embed-aspect-16-9 .wp-block-embed__wrapper:before {
  padding-top: 56.25%;
}

.wp-block-embed.is-type-video.wp-embed-aspect-21-9 .wp-block-embed__wrapper:before {
  padding-top: 42.85%;
}

.wp-block-embed.is-type-video.wp-embed-aspect-18-9 .wp-block-embed__wrapper:before,
.wp-block-embed.is-type-video.wp-embed-aspect-2-1 .wp-block-embed__wrapper:before {
  padding-top: 50%;
}

.wp-block-embed.is-type-video.wp-embed-aspect-4-3 .wp-block-embed__wrapper:before {
  padding-top: 75%;
}

.wp-block-embed.is-type-video.wp-embed-aspect-1-1 .wp-block-embed__wrapper:before {
  padding-top: 100%;
}

.wp-block-embed.is-type-video.wp-embed-aspect-3-4 .wp-block-embed__wrapper:before {
  padding-top: 133.33%;
}

.wp-block-embed.is-type-video.wp-embed-aspect-9-16 .wp-block-embed__wrapper:before {
  padding-top: 177.77%;
}

.wp-block-embed.is-type-video.wp-embed-aspect-9-18 .wp-block-embed__wrapper:before,
.wp-block-embed.is-type-video.wp-embed-aspect-1-2 .wp-block-embed__wrapper:before {
  padding-top: 200%;
}

.wp-block-embed.is-type-video.wp-embed-aspect-9-21 .wp-block-embed__wrapper:before {
  padding-top: 233.33%;
}

/* WordPress Playlist */
.wp-playlist-light {
  background: var(--theme-color-bg_color);
  border-color: var(--theme-color-bd_color);
  color: var(--theme-color-title);
}
.wp-playlist-light .wp-playlist-caption {
  color: var(--theme-color-title);
}
.wp-playlist-light .wp-playlist-playing {
  background: transparent;
  color: var(--theme-color-title);
}

.wp-playlist-item {
  border-color: var(--theme-color-bd_color);
}

.wp-playlist .wp-playlist-current-item img {
  background-color: var(--theme-color-bg_color);
}

/* Cover image */
.wp-block-cover-image a,
.wp-block-cover a {
  text-decoration: underline;
}

/* Media & Text */
.wp-block-media-text .has-medium-font-size {
  line-height: 1.5em;
}
.wp-block-media-text .has-large-font-size {
  line-height: 1.4em;
}
.wp-block-media-text .has-huge-font-size {
  line-height: 1.3em;
}

/* Custom font size in blocks */
.has-large-font-size,
.has-huge-font-size,
.has-x-large-font-size {
  line-height: 1.4em;
}

/* Groups */
.wp-block-group.has-background {
  margin-bottom: var(--theme-font-p_margin-bottom);
}

/* Alignment */
.alignleft {
  display: inline-block;
  vertical-align: top;
  float: left;
  margin-right: 2em !important;
  margin-bottom: 1em !important;
  margin-top: 0.5em !important;
}

.alignright {
  display: inline-block;
  vertical-align: top;
  float: right;
  margin-left: 2em !important;
  margin-bottom: 1em !important;
  margin-top: 0.5em !important;
}

.aligncenter {
  display: block;
  text-align: center;
  clear: both;
  margin-left: auto !important;
  margin-right: auto !important;
  margin-bottom: 1em !important;
}

figure.alignleft,
figure.alignright {
  margin-top: 0.5em !important;
}

.wp-block-gallery[class*="align"] {
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.has-left-content {
  text-align: left;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.has-center-content {
  text-align: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.has-right-content {
  text-align: right;
  -webkit-justify-content: flex-end;
  -ms-flex-pack: end;
  justify-content: flex-end;
}

/* Align left and right inside narrow content without sidebars */
.sidebar_hide.narrow_content .alignleft.is-style-alignfar,
.sidebar_hide.narrow_content .is-style-alignfar > .alignleft,
.sidebar_hide.narrow_content .alignright.is-style-alignfar,
.sidebar_hide.narrow_content .is-style-alignfar > .alignright {
  max-width: calc( ( var(--theme-var-page) - var(--theme-var-content_narrow) ) / 2 - var(--theme-var-grid_gap) );
}

.sidebar_hide.narrow_content .alignleft.is-style-alignfar,
.sidebar_hide.narrow_content .is-style-alignfar > .alignleft {
  float: left;
  margin: 1em 1em 1em calc( ( var(--theme-var-page) - var(--theme-var-content_narrow) ) / -2 );
}

.sidebar_hide.narrow_content .alignright.is-style-alignfar,
.sidebar_hide.narrow_content .is-style-alignfar > .alignright {
  float: right;
  margin: 1em calc( ( var(--theme-var-page) - var(--theme-var-content_narrow) ) / -2 ) 1em 2em;
}

.sidebar_hide.narrow_content .wp-block-image > .alignleft.is-style-alignfar,
.sidebar_hide.narrow_content .wp-block-image.is-style-alignfar > .alignleft,
.sidebar_hide.narrow_content .wp-block-image > .alignright.is-style-alignfar,
.sidebar_hide.narrow_content .wp-block-image.is-style-alignfar > .alignright {
  max-width: none !important;
}

/* Align left and right inside normal content without sidebars */
.sidebar_hide.normal_content .alignleft.is-style-alignfar,
.sidebar_hide.normal_content .is-style-alignfar > .alignleft {
  float: left;
  margin: 1em 1em 1em calc( ( var(--theme-var-page) - var(--theme-var-content) ) / -2 );
}

.sidebar_hide.normal_content .alignright.is-style-alignfar,
.sidebar_hide.normal_content .is-style-alignfar > .alignright {
  float: right;
  margin: 1em calc( ( var(--theme-var-page) - var(--theme-var-content) ) / -2 ) 1em 2em;
}

.sidebar_hide.normal_content .wp-block-image > .alignleft.is-style-alignfar,
.sidebar_hide.normal_content .wp-block-image.is-style-alignfar > .alignleft,
.sidebar_hide.normal_content .wp-block-image > .alignright.is-style-alignfar,
.sidebar_hide.normal_content .wp-block-image.is-style-alignfar > .alignright {
  max-width: none !important;
}

/* Wide and Full blocks */
.alignfull > img,
.alignwide > img {
  max-width: none;
  width: 100%;
}

body.sidebar_hide .alignwide {
  position: relative;
  z-index: 1;
  left: calc( -88vw / 2 + 100% / 2 );
  width: 88vw;
  max-width: none;
}

body.sidebar_hide.narrow_content .alignwide,
body.sidebar_hide.normal_content .alignwide {
  left: calc( var(--theme-var-page) / -2 + 50% );
  width: var(--theme-var-page);
}

body.sidebar_hide .alignfull {
  position: relative;
  z-index: 1;
  margin-left: calc( -100vw / 2 + 100% / 2 + 8px );
  margin-right: calc( -100vw / 2 + 100% / 2 + 8px );
  width: calc( 100vw - 16px );
  max-width: calc( 100vw - 16px );
}

/* 3. Form fields settings
-------------------------------------------------------------- */
/* Common rules */
form {
  margin-bottom: 0;
  position: relative;
}

button, input, optgroup, select, textarea, textarea.wp-editor-area {
  font-family: inherit;
  font-size: 1em;
  /* Corrects font size not being inherited in all browsers */
  margin: 0;
  /* Addresses margins set differently in IE6/7, F3/4, S5, Chrome */
  vertical-align: baseline;
  /* Improves appearance and consistency in all browsers */
}

button {
  overflow: visible;
}

/* Buttons */
button:where(:not(.components-button):not([class*="wp-block-social"]):not([id="elementor-editor-button"])),
input[type="button"],
input[type="reset"],
input[type="submit"],
.theme_button,
.post_item .more-link,
.wp-block-button__link,
.elementor-button,
.sc_button_default {
  -webkit-appearance: none;
  cursor: pointer;
  display: inline-block;
  vertical-align: top;
  -webkit-box-sizing: border-box;
  -ms-box-sizing: border-box;
  box-sizing: border-box;
  /* white-space: nowrap; Allow button text to wrap */
  height: auto;
  max-width: 100%;
  color: var(--theme-font-button_color, var(--theme-color-bg_color));
  border-color: var(--theme-font-button_border-color, var(--theme-color-link));
  background-color: var(--theme-font-button_background-color, var(--theme-color-link));
}

/* WP*/
.wp-block-button__link {
  white-space: normal;
}

.wp-block-button.is-style-squared .wp-block-button__link {
  -webkit-border-radius: 0;
  -ms-border-radius: 0;
  border-radius: 0;
}

/* Buttons hover */
button:where(:not(.components-button):not([class*="wp-block-social"]):not([id="elementor-editor-button"])):hover,
button:where(:not(.components-button):not([class*="wp-block-social"]):not([id="elementor-editor-button"])):focus,
input[type="submit"]:hover,
input[type="submit"]:focus,
input[type="reset"]:hover,
input[type="reset"]:focus,
input[type="button"]:hover,
input[type="button"]:focus,
.theme_button:hover,
.theme_button:focus,
.post_item .more-link:hover,
.wp-block-button:not(.is-style-outline) .wp-block-button__link:hover,
.wp-block-button:not(.is-style-outline) .wp-block-button__link:focus,
.elementor-button:hover,
.elementor-button:focus,
.sc_button_default:hover,
.sc_button_default:focus {
  color: var(--theme-font-button_color-hover, var(--theme-color-bg_color));
  border-color: var(--theme-font-button_border-color-hover, var(--theme-color-hover));
  background-color: var(--theme-font-button_background-color-hover, var(--theme-color-hover));
}

/* Visited buttons */
.elementor-button:visited {
  color: var(--theme-font-button_color, var(--theme-color-bg_color));
}

/* Disabled buttons */
button[disabled],
input[type="submit"][disabled],
input[type="button"][disabled],
a.sc_button[disabled],
a.theme_button[disabled],
button[disabled]:hover,
input[type="submit"][disabled]:hover,
input[type="button"][disabled]:hover,
a.sc_button[disabled]:hover,
a.theme_button[disabled]:hover {
  color: var(--theme-color-text) !important;
  border-color: var(--theme-color-meta) !important;
  background: var(--theme-color-meta) !important;
}

/* WP Bordered button */
.wp-block-button.is-style-outline .wp-block-button__link {
  background: none;
  border-width: 2px;
  border-style: solid;
}

.wp-block-button.is-style-outline .wp-block-button__link:not(.has-text-color) {
  color: var(--theme-color-link);
  border-color: var(--theme-color-link);
}

.wp-block-button.is-style-outline .wp-block-button__link:hover,
.wp-block-button.is-style-outline .wp-block-button__link:focus {
  color: var(--theme-color-hover) !important;
  border-color: var(--theme-color-hover) !important;
}

/* ThemeREX Addons Simple button */
.sc_button.sc_button_simple {
  font-size: 1rem;
  border: none !important;
  background: transparent !important;
  -webkit-border-radius: 0;
  -ms-border-radius: 0;
  border-radius: 0;
  padding: 0 1.8em 0 0;
  color: var(--theme-color-link);
}
.sc_button.sc_button_simple:hover, .sc_button.sc_button_simple:focus {
  color: var(--theme-color-hover);
}

/* Text fields */
.theme_form_field_text,
input[type="text"],
input[type="number"],
input[type="email"],
input[type="url"],
input[type="tel"],
input[type="search"],
input[type="password"],
select,
.select2-container.select2-container--default span.select2-choice,
.select2-container.select2-container--default span.select2-selection,
.select2-container.select2-container--default .select2-selection--multiple,
.select2-container--default .select2-search--dropdown .select2-search__field,
textarea,
textarea.wp-editor-area {
  color: var(--theme-font-input_color, var(--theme-color-text));
  border-color: var(--theme-font-input_border-color, var(--theme-color-bd_color));
  background-color: var(--theme-font-input_background-color, var(--theme-color-bg_color));
}

.theme_form_field_text:focus,
.theme_form_field_text.filled,
input[type="text"]:focus,
input[type="text"].filled,
input[type="number"]:focus,
input[type="number"].filled,
input[type="email"]:focus,
input[type="email"].filled,
input[type="url"]:focus,
input[type="url"].filled,
input[type="tel"]:focus,
input[type="tel"].filled,
input[type="search"]:focus,
input[type="search"].filled,
input[type="password"]:focus,
input[type="password"].filled,
select:hover,
select:focus,
select option:hover,
select option:focus,
select.select2-hidden-accessible.filled + .select2-container.select2-container--default span.select2-selection--single,
.select2-container.select2-container--default span.select2-selection--single:hover,
.select2-container.select2-container--focus span.select2-selection--single,
.select2-container.select2-container--open span.select2-selection--single,
select.select2-hidden-accessible.filled + .select2-container.select2-container--default span.select2-choice,
.select2-container.select2-container--default span.select2-choice:hover,
.select2-container.select2-container--focus span.select2-choice,
.select2-container.select2-container--open span.select2-choice,
select.select2-hidden-accessible.filled + .select2-container.select2-container--default span.select2-selection--multiple,
.select2-container.select2-container--default span.select2-selection--multiple:hover,
.select2-container.select2-container--focus span.select2-selection--multiple,
.select2-container.select2-container--open span.select2-selection--multiple,
.select2-container .select2-dropdown,
.select2-container.select2-container--focus span.select2-selection,
.select2-container.select2-container--open span.select2-selection,
textarea:focus,
textarea.filled,
textarea.wp-editor-area:focus,
textarea.wp-editor-area.filled {
  color: var(--theme-font-input_color-focus, var(--theme-color-title));
  border-color: var(--theme-font-input_border-color-focus, var(--theme-color-meta));
  background-color: var(--theme-font-input_background-color-focus, var(--theme-color-bg_color));
}

textarea,
textarea.wp-editor-area {
  overflow: auto;
  /* Removes default vertical scrollbar in IE6/7/8/9 */
  vertical-align: top;
  /* Improves readability and alignment in all browsers */
}

textarea.wp-editor-area {
  -webkit-border-radius: var(--theme-font-input_border-radius, 0);
  -ms-border-radius: var(--theme-font-input_border-radius, 0);
  border-radius: var(--theme-font-input_border-radius, 0);
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

/* Post password field */
input[type="password"][name="post_password"] {
  outline: none;
}

/* Placeholders */
.theme_form_field_placeholder {
  text-overflow: ellipsis;
  opacity: 1;
  color: var(--theme-color-meta);
}

input[placeholder]::-webkit-input-placeholder {
  text-overflow: ellipsis;
  opacity: 1;
  color: var(--theme-color-meta);
}

textarea[placeholder]::-webkit-input-placeholder {
  text-overflow: ellipsis;
  opacity: 1;
  color: var(--theme-color-meta);
}

input[placeholder]::-moz-placeholder {
  text-overflow: ellipsis;
  opacity: 1;
  color: var(--theme-color-meta);
}

textarea[placeholder]::-moz-placeholder {
  text-overflow: ellipsis;
  opacity: 1;
  color: var(--theme-color-meta);
}

input[placeholder]:-ms-input-placeholder {
  text-overflow: ellipsis;
  opacity: 1;
  color: var(--theme-color-meta);
}

textarea[placeholder]:-ms-input-placeholder {
  text-overflow: ellipsis;
  opacity: 1;
  color: var(--theme-color-meta);
}

input[placeholder]::placeholder {
  text-overflow: ellipsis;
  opacity: 1;
  color: var(--theme-color-meta);
}

textarea[placeholder]::placeholder {
  text-overflow: ellipsis;
  opacity: 1;
  color: var(--theme-color-meta);
}

textarea, textarea.wp-editor-area,
select, option,
.theme_form_field_text,
input[type="text"],
input[type="number"],
input[type="email"],
input[type="url"],
input[type="tel"],
input[type="search"],
input[type="password"],
input[type="checkbox"],
input[type="radio"] {
  -webkit-transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
  -ms-transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
  -webkit-box-sizing: border-box;
  -ms-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-border-radius: 0;
  -ms-border-radius: 0;
  border-radius: 0;
}

option {
  -webkit-box-sizing: border-box;
  -ms-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-border-radius: 0;
  -ms-border-radius: 0;
  border-radius: 0;
  color: var(--theme-color-title);
  background-color: var(--theme-color-bg_color);
  font-size: inherit;
  font-weight: inherit;
}

optgroup {
  font-style: normal;
}
optgroup > option {
  font-weight: normal;
}

button[disabled],
html input[disabled] {
  cursor: default !important;
}

.theme_form_field_text,
input[type="text"],
input[type="number"],
input[type="email"],
input[type="url"],
input[type="tel"],
input[type="password"],
input[type="search"],
select,
textarea,
textarea.wp-editor-area {
  -webkit-appearance: none;
  outline: none;
  resize: none;
}

button:focus,
.theme_form_field_text:focus,
input[type="text"]:focus,
input[type="email"]:focus,
input[type="url"]:focus,
input[type="password"]:focus,
input[type="search"]:focus,
select:focus,
textarea:focus,
textarea.wp-editor-area:focus {
  outline: 0;
}

.theme_form_field_text,
input[type="text"],
input[type="number"],
input[type="email"],
input[type="url"],
input[type="tel"],
input[type="password"] {
  -webkit-appearance: textfield;
}

input[type="search"] {
  -webkit-appearance: none;
}

input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
  /* Corrects inner padding displayed oddly in S5, Chrome on OSX */
}

body.show_outline button:focus,
body.show_outline input:focus,
body.show_outline select:focus,
body.show_outline textarea:focus,
body.show_outline textarea.wp-editor-area:focus {
  outline: thin dotted !important;
}

/* Radio buttons and checkboxes */
input[type="radio"],
input[type="checkbox"] {
  clip: rect(1px, 1px, 1px, 1px);
  position: absolute !important;
  margin: 0;
  padding: 0;
}

input[type="radio"] + label,
input[type="checkbox"] + label {
  color: var(--theme-color-meta);
  position: relative;
  padding-left: 24px;
  font-size: 14px;
  line-height: 20px;
  display: inline-block;
  vertical-align: top;
}

input[type="radio"] + label:before,
input[type="checkbox"] + label:before,
label > input[type="radio"]:before,
label > input[type="checkbox"]:before,
input[type="radio"].radio:before,
input[type="checkbox"].checkbox:before {
  content: '';
  font-size: 20px;
  display: block;
  text-align: center;
  border: 1px solid var(--theme-color-bd_color);
  width: 16px;
  height: 16px;
  line-height: 16px;
  -webkit-border-radius: 4px;
  -ms-border-radius: 4px;
  border-radius: 4px;
  position: absolute;
  z-index: 1;
  top: 2px;
  left: 0;
  -webkit-box-sizing: border-box;
  -ms-box-sizing: border-box;
  box-sizing: border-box;
}

input[type="checkbox"]:checked + label:before,
input[type="radio"]:checked + label:before,
label > input[type="radio"]:checked:before,
label > input[type="checkbox"]:checked:before,
input[type="radio"].radio:checked:before,
input[type="checkbox"].checkbox:checked:before {
  background: radial-gradient(circle, var(--theme-color-title) 55%, var(--theme-color-bg_color) 65%);
  box-shadow: inset 0 0 0 2px var(--theme-color-bg_color);
}

label > input[type="radio"],
label > input[type="checkbox"],
input[type="radio"].radio,
input[type="checkbox"].checkbox {
  display: inline-block;
  vertical-align: baseline;
  position: static !important;
  clip: auto;
  -webkit-appearance: none;
  -moz-appearance: none;
}
label > input[type="radio"]:before,
label > input[type="checkbox"]:before,
input[type="radio"].radio:before,
input[type="checkbox"].checkbox:before {
  position: relative;
}

body.show_outline input[type="radio"]:focus + label:before,
body.show_outline input[type="checkbox"]:focus + label:before,
body.show_outline label > input[type="radio"]:focus:before,
body.show_outline label > input[type="checkbox"]:focus:before,
body.show_outline input[type="radio"].radio:focus:before,
body.show_outline input[type="checkbox"].checkbox:focus:before {
  outline: 0 !important;
  box-shadow: 0 0 0 1px var(--theme-color-title);
}

/* Select container (dropdown) */
select {
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

select::-ms-expand {
  display: none;
}

select {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3E%3Ccircle cx='15' cy='15' r='15' fill='rgba(255, 255, 255, 0.3)'/%3E%3Cpath d='M9 13l6 6 6-6' stroke='rgba(0, 0, 0, 1)' stroke-width='2' fill='none' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  background-position: right 10px center;
  background-size: 18px;
  background-repeat: no-repeat;
}
body select {
  padding-right: 35px;
}

/* Select2 - Advanced select with search */
.select2-container {
  width: 100% !important;
}
.select_container .select2-container {
  z-index: 1;
}
.select2-container .select2-selection--single {
  -webkit-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  transition: all 0.3s ease;
  margin: 0;
}
.select2-container .select2-dropdown {
  overflow: hidden;
  -webkit-border-radius: var(--theme-font-input_border-radius, 0);
  -ms-border-radius: var(--theme-font-input_border-radius, 0);
  border-radius: var(--theme-font-input_border-radius, 0);
}

.select2-results__option {
  color: var(--theme-color-title);
}

.select2-container--default .select2-selection--single .select2-selection__placeholder {
  color: inherit;
}
.select2-container--default .select2-selection--single span.select2-selection__rendered {
  line-height: var(--theme-font-input_line-height);
  padding-left: 0;
  color: inherit;
  padding: 0;
}
.select2-container--default .select2-results__option--highlighted[aria-selected],
.select2-container--default .select2-results__option--highlighted[data-selected] {
  color: var(--theme-color-bg_color);
  background: var(--theme-color-link);
}
.select2-container--default .select2-selection--single .select2-selection__arrow {
  width: 20px;
  height: 20px;
  right: 10px;
}
.select2-container--default .select2-selection--single .select2-selection__arrow b {
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3E%3Ccircle cx='15' cy='15' r='15' fill='rgba(255, 255, 255, 0.3)'/%3E%3Cpath d='M9 13l6 6 6-6' stroke='rgba(0, 0, 0, 1)' stroke-width='2' fill='none' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  background-position: center;
  background-size: 18px;
  background-repeat: no-repeat;
  margin: 0;
  border: none;
  width: 100%;
  height: 100%;
}
.select2-container--default.select2-container .select2-selection--single.select2-selection {
  padding-right: 35px;
}

/* Required fields */
label.required:after {
  content: '*';
  display: none;
  vertical-align: text-top;
  font-size: 80%;
  color: red;
}

/* 4. WP styles and Screen readers
-------------------------------------------------------------- */
.screen-reader-text {
  clip: rect(1px, 1px, 1px, 1px);
  position: absolute !important;
  margin: 0 !important;
  padding: 0 !important;
}
.screen-reader-text:hover, .screen-reader-text:active, .screen-reader-text:focus {
  display: block;
  top: 5px;
  left: 5px;
  width: auto;
  height: auto;
  font-size: 0.8em;
  line-height: normal;
  padding: 1em 1.5em;
  color: #21759b;
  background-color: #f1f1f1;
  clip: auto !important;
  text-decoration: none;
  /* Above WP toolbar */
  z-index: 100000;
  -webkit-box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
  -ms-box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
  box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
}

.elementra_skip_link {
  position: fixed;
  z-index: 999999;
  top: 6px;
  left: 6px;
  -webkit-transform: translateY(-300px);
  -ms-transform: translateY(-300px);
  transform: translateY(-300px);
  -webkit-transition: -webkit-transform 0.3s ease 0s;
  -ms-transition: -ms-transform 0.3s ease 0s;
  transition: transform 0.3s ease 0s;
}

.elementra_skip_link:focus {
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  transform: translateY(0);
  outline-offset: -1px;
  display: block;
  width: auto;
  height: auto;
  font-size: 1em;
  line-height: normal;
  font-weight: 400;
  padding: 1em 1.5em;
  background: #f1f1f1;
  color: #0073aa;
  box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
}

a.elementra_skip_link_anchor {
  position: absolute;
  width: 0;
  height: 0;
}

/* 5. Theme grid
-------------------------------------------------------------- */
.row, .columns_wrap, .trx_addons_columns_wrap {
  margin-left: 0px;
  margin-right: calc( -1 * var(--theme-var-grid_gap) );
}

.row > [class*="column-"],
.columns_wrap > [class*="column-"],
.trx_addons_columns_wrap > [class*="trx_addons_column-"] {
  display: inline-block;
  vertical-align: top;
  position: relative;
  z-index: 20;
  min-height: 1px;
  padding-left: 0px;
  padding-right: var(--theme-var-grid_gap);
  -webkit-box-sizing: border-box;
  -ms-box-sizing: border-box;
  box-sizing: border-box;
}

.row.columns_padding_left,
.columns_wrap.columns_padding_left,
.trx_addons_columns_wrap.columns_padding_left {
  margin-left: calc( -1 * var(--theme-var-grid_gap) );
  margin-right: 0;
}

.row.columns_padding_left > [class*="column-"],
.row > [class*="column-"].columns_padding_left,
.columns_wrap.columns_padding_left > [class*="column-"],
.columns_wrap > [class*="column-"].columns_padding_left,
.trx_addons_columns_wrap.columns_padding_left > [class*="trx_addons_column-"],
.trx_addons_columns_wrap > [class*="trx_addons_column-"].columns_padding_left {
  padding-left: var(--theme-var-grid_gap);
  padding-right: 0;
}

.row.columns_padding_right,
.columns_wrap.columns_padding_right,
.trx_addons_columns_wrap.columns_padding_right {
  margin-left: 0;
  margin-right: calc( -1 * var(--theme-var-grid_gap) );
}

.row.columns_padding_right > [class*="column-"],
.row > [class*="column-"].columns_padding_right,
.columns_wrap.columns_padding_right > [class*="column-"],
.columns_wrap > [class*="column-"].columns_padding_right,
.trx_addons_columns_wrap.columns_padding_right > [class*="trx_addons_column-"],
.trx_addons_columns_wrap > [class*="trx_addons_column-"].columns_padding_right {
  padding-left: 0;
  padding-right: var(--theme-var-grid_gap);
}

.row.columns_padding_center,
.columns_wrap.columns_padding_center,
.trx_addons_columns_wrap.columns_padding_center {
  margin-left: calc( -1 * var(--theme-var-grid_gap) / 2 );
  margin-right: calc( -1 * var(--theme-var-grid_gap) / 2 );
}

.row.columns_padding_center > [class*="column-"],
.row > [class*="column-"].columns_padding_center,
.columns_wrap.columns_padding_center > [class*="column-"],
.columns_wrap > [class*="column-"].columns_padding_center,
.trx_addons_columns_wrap.columns_padding_center > [class*="trx_addons_column-"],
.trx_addons_columns_wrap > [class*="trx_addons_column-"].columns_padding_center {
  padding-left: calc( var(--theme-var-grid_gap) / 2 );
  padding-right: calc( var(--theme-var-grid_gap) / 2 );
}

.row.columns_padding_bottom > [class*="column-"],
.row > [class*="column-"].columns_padding_bottom,
.columns_wrap.columns_padding_bottom > [class*="column-"],
.columns_wrap > [class*="column-"].columns_padding_bottom,
.trx_addons_columns_wrap.columns_padding_bottom > [class*="trx_addons_column-"],
.trx_addons_columns_wrap > [class*="trx_addons_column-"].columns_padding_bottom {
  padding-bottom: var(--theme-var-grid_gap);
}

.row.columns_padding_bottom.columns_in_single_row > [class*="column-"],
.columns_wrap.columns_padding_bottom.columns_in_single_row > [class*="column-"],
.trx_addons_columns_wrap.columns_padding_bottom.columns_in_single_row > [class*="trx_addons_column-"] {
  padding-bottom: 0;
}

.row.no_margin,
.columns_wrap.no_margin,
.sc_blogger.no_margin .row,
.sc_blogger.no_margin .columns_wrap {
  margin-left: 0 !important;
  margin-right: 0 !important;
}

.row.no_margin > [class*="column-"],
.columns_wrap.no_margin > [class*="column-"],
.sc_blogger.no_margin .row > [class*="column-"],
.sc_blogger.no_margin .columns_wrap > [class*="column-"] {
  padding: 0 !important;
}

/* Columns, push, pull and offset sizes */
.column-1,
.column-1_1 {
  width: 100%;
}

.column-1_2 {
  width: 50%;
}

.push-1_2 {
  left: 50%;
}

.pull-1_2 {
  right: 50%;
}

.offset-1_2 {
  margin-left: 50%;
}

.column-1_3 {
  width: 33.3333333333%;
}

.push-1_3 {
  left: 33.3333333333%;
}

.pull-1_3 {
  right: 33.3333333333%;
}

.offset-1_3 {
  margin-left: 33.3333333333%;
}

.column-1_4 {
  width: 25%;
}

.push-1_4 {
  left: 25%;
}

.pull-1_4 {
  right: 25%;
}

.offset-1_4 {
  margin-left: 25%;
}

.column-1_5 {
  width: 20%;
}

.push-1_5 {
  left: 20%;
}

.pull-1_5 {
  right: 20%;
}

.offset-1_5 {
  margin-left: 20%;
}

.column-1_6 {
  width: 16.6666666667%;
}

.push-1_6 {
  left: 16.6666666667%;
}

.pull-1_6 {
  right: 16.6666666667%;
}

.offset-1_6 {
  margin-left: 16.6666666667%;
}

.column-1_7 {
  width: 14.2857142857%;
}

.push-1_7 {
  left: 14.2857142857%;
}

.pull-1_7 {
  right: 14.2857142857%;
}

.offset-1_7 {
  margin-left: 14.2857142857%;
}

.column-1_8 {
  width: 12.5%;
}

.push-1_8 {
  left: 12.5%;
}

.pull-1_8 {
  right: 12.5%;
}

.offset-1_8 {
  margin-left: 12.5%;
}

.column-1_9 {
  width: 11.1111111111%;
}

.push-1_9 {
  left: 11.1111111111%;
}

.pull-1_9 {
  right: 11.1111111111%;
}

.offset-1_9 {
  margin-left: 11.1111111111%;
}

.column-1_10 {
  width: 10%;
}

.push-1_10 {
  left: 10%;
}

.pull-1_10 {
  right: 10%;
}

.offset-1_10 {
  margin-left: 10%;
}

.column-1_11 {
  width: 9.0909090909%;
}

.push-1_11 {
  left: 9.0909090909%;
}

.pull-1_11 {
  right: 9.0909090909%;
}

.offset-1_11 {
  margin-left: 9.0909090909%;
}

.column-1_12 {
  width: 8.3333333333%;
}

.push-1_12 {
  left: 8.3333333333%;
}

.pull-1_12 {
  right: 8.3333333333%;
}

.offset-1_12 {
  margin-left: 8.3333333333%;
}

.column-2_2 {
  width: 100%;
}

.column-2_3 {
  width: 66.6666666667%;
}

.push-2_3 {
  left: 66.6666666667%;
}

.pull-2_3 {
  right: 66.6666666667%;
}

.offset-2_3 {
  margin-left: 66.6666666667%;
}

.column-2_4 {
  width: 50%;
}

.push-2_4 {
  left: 50%;
}

.pull-2_4 {
  right: 50%;
}

.offset-2_4 {
  margin-left: 50%;
}

.column-2_5 {
  width: 40%;
}

.push-2_5 {
  left: 40%;
}

.pull-2_5 {
  right: 40%;
}

.offset-2_5 {
  margin-left: 40%;
}

.column-2_6 {
  width: 33.3333333333%;
}

.push-2_6 {
  left: 33.3333333333%;
}

.pull-2_6 {
  right: 33.3333333333%;
}

.offset-2_6 {
  margin-left: 33.3333333333%;
}

.column-2_7 {
  width: 28.5714285714%;
}

.push-2_7 {
  left: 28.5714285714%;
}

.pull-2_7 {
  right: 28.5714285714%;
}

.offset-2_7 {
  margin-left: 28.5714285714%;
}

.column-2_8 {
  width: 25%;
}

.push-2_8 {
  left: 25%;
}

.pull-2_8 {
  right: 25%;
}

.offset-2_8 {
  margin-left: 25%;
}

.column-2_9 {
  width: 22.2222222222%;
}

.push-2_9 {
  left: 22.2222222222%;
}

.pull-2_9 {
  right: 22.2222222222%;
}

.offset-2_9 {
  margin-left: 22.2222222222%;
}

.column-2_10 {
  width: 20%;
}

.push-2_10 {
  left: 20%;
}

.pull-2_10 {
  right: 20%;
}

.offset-2_10 {
  margin-left: 20%;
}

.column-2_11 {
  width: 18.1818181818%;
}

.push-2_11 {
  left: 18.1818181818%;
}

.pull-2_11 {
  right: 18.1818181818%;
}

.offset-2_11 {
  margin-left: 18.1818181818%;
}

.column-2_12 {
  width: 16.6666666667%;
}

.push-2_12 {
  left: 16.6666666667%;
}

.pull-2_12 {
  right: 16.6666666667%;
}

.offset-2_12 {
  margin-left: 16.6666666667%;
}

.column-3_3 {
  width: 100%;
}

.column-3_4 {
  width: 75%;
}

.push-3_4 {
  left: 75%;
}

.pull-3_4 {
  right: 75%;
}

.offset-3_4 {
  margin-left: 75%;
}

.column-3_5 {
  width: 60%;
}

.push-3_5 {
  left: 60%;
}

.pull-3_5 {
  right: 60%;
}

.offset-3_5 {
  margin-left: 60%;
}

.column-3_6 {
  width: 50%;
}

.push-3_6 {
  left: 50%;
}

.pull-3_6 {
  right: 50%;
}

.offset-3_6 {
  margin-left: 50%;
}

.column-3_7 {
  width: 42.8571428571%;
}

.push-3_7 {
  left: 42.8571428571%;
}

.pull-3_7 {
  right: 42.8571428571%;
}

.offset-3_7 {
  margin-left: 42.8571428571%;
}

.column-3_8 {
  width: 37.5%;
}

.push-3_8 {
  left: 37.5%;
}

.pull-3_8 {
  right: 37.5%;
}

.offset-3_8 {
  margin-left: 37.5%;
}

.column-3_9 {
  width: 33.3333333333%;
}

.push-3_9 {
  left: 33.3333333333%;
}

.pull-3_9 {
  right: 33.3333333333%;
}

.offset-3_9 {
  margin-left: 33.3333333333%;
}

.column-3_10 {
  width: 30%;
}

.push-3_10 {
  left: 30%;
}

.pull-3_10 {
  right: 30%;
}

.offset-3_10 {
  margin-left: 30%;
}

.column-3_11 {
  width: 27.2727272727%;
}

.push-3_11 {
  left: 27.2727272727%;
}

.pull-3_11 {
  right: 27.2727272727%;
}

.offset-3_11 {
  margin-left: 27.2727272727%;
}

.column-3_12 {
  width: 25%;
}

.push-3_12 {
  left: 25%;
}

.pull-3_12 {
  right: 25%;
}

.offset-3_12 {
  margin-left: 25%;
}

.column-4_4 {
  width: 100%;
}

.column-4_5 {
  width: 80%;
}

.push-4_5 {
  left: 80%;
}

.pull-4_5 {
  right: 80%;
}

.offset-4_5 {
  margin-left: 80%;
}

.column-4_6 {
  width: 66.6666666667%;
}

.push-4_6 {
  left: 66.6666666667%;
}

.pull-4_6 {
  right: 66.6666666667%;
}

.offset-4_6 {
  margin-left: 66.6666666667%;
}

.column-4_7 {
  width: 57.1428571429%;
}

.push-4_7 {
  left: 57.1428571429%;
}

.pull-4_7 {
  right: 57.1428571429%;
}

.offset-4_7 {
  margin-left: 57.1428571429%;
}

.column-4_8 {
  width: 50%;
}

.push-4_8 {
  left: 50%;
}

.pull-4_8 {
  right: 50%;
}

.offset-4_8 {
  margin-left: 50%;
}

.column-4_9 {
  width: 44.4444444444%;
}

.push-4_9 {
  left: 44.4444444444%;
}

.pull-4_9 {
  right: 44.4444444444%;
}

.offset-4_9 {
  margin-left: 44.4444444444%;
}

.column-4_10 {
  width: 40%;
}

.push-4_10 {
  left: 40%;
}

.pull-4_10 {
  right: 40%;
}

.offset-4_10 {
  margin-left: 40%;
}

.column-4_11 {
  width: 36.3636363636%;
}

.push-4_11 {
  left: 36.3636363636%;
}

.pull-4_11 {
  right: 36.3636363636%;
}

.offset-4_11 {
  margin-left: 36.3636363636%;
}

.column-4_12 {
  width: 33.3333333333%;
}

.push-4_12 {
  left: 33.3333333333%;
}

.pull-4_12 {
  right: 33.3333333333%;
}

.offset-4_12 {
  margin-left: 33.3333333333%;
}

.column-5_5 {
  width: 100%;
}

.column-5_6 {
  width: 83.3333333333%;
}

.push-5_6 {
  left: 83.3333333333%;
}

.pull-5_6 {
  right: 83.3333333333%;
}

.offset-5_6 {
  margin-left: 83.3333333333%;
}

.column-5_7 {
  width: 71.4285714286%;
}

.push-5_7 {
  left: 71.4285714286%;
}

.pull-5_7 {
  right: 71.4285714286%;
}

.offset-5_7 {
  margin-left: 71.4285714286%;
}

.column-5_8 {
  width: 62.5%;
}

.push-5_8 {
  left: 62.5%;
}

.pull-5_8 {
  right: 62.5%;
}

.offset-5_8 {
  margin-left: 62.5%;
}

.column-5_9 {
  width: 55.5555555556%;
}

.push-5_9 {
  left: 55.5555555556%;
}

.pull-5_9 {
  right: 55.5555555556%;
}

.offset-5_9 {
  margin-left: 55.5555555556%;
}

.column-5_10 {
  width: 50%;
}

.push-5_10 {
  left: 50%;
}

.pull-5_10 {
  right: 50%;
}

.offset-5_10 {
  margin-left: 50%;
}

.column-5_11 {
  width: 45.4545454545%;
}

.push-5_11 {
  left: 45.4545454545%;
}

.pull-5_11 {
  right: 45.4545454545%;
}

.offset-5_11 {
  margin-left: 45.4545454545%;
}

.column-5_12 {
  width: 41.6666666667%;
}

.push-5_12 {
  left: 41.6666666667%;
}

.pull-5_12 {
  right: 41.6666666667%;
}

.offset-5_12 {
  margin-left: 41.6666666667%;
}

.column-6_6 {
  width: 100%;
}

.column-6_7 {
  width: 85.7142857143%;
}

.push-6_7 {
  left: 85.7142857143%;
}

.pull-6_7 {
  right: 85.7142857143%;
}

.offset-6_7 {
  margin-left: 85.7142857143%;
}

.column-6_8 {
  width: 75%;
}

.push-6_8 {
  left: 75%;
}

.pull-6_8 {
  right: 75%;
}

.offset-6_8 {
  margin-left: 75%;
}

.column-6_9 {
  width: 66.6666666667%;
}

.push-6_9 {
  left: 66.6666666667%;
}

.pull-6_9 {
  right: 66.6666666667%;
}

.offset-6_9 {
  margin-left: 66.6666666667%;
}

.column-6_10 {
  width: 60%;
}

.push-6_10 {
  left: 60%;
}

.pull-6_10 {
  right: 60%;
}

.offset-6_10 {
  margin-left: 60%;
}

.column-6_11 {
  width: 54.5454545455%;
}

.push-6_11 {
  left: 54.5454545455%;
}

.pull-6_11 {
  right: 54.5454545455%;
}

.offset-6_11 {
  margin-left: 54.5454545455%;
}

.column-6_12 {
  width: 50%;
}

.push-6_12 {
  left: 50%;
}

.pull-6_12 {
  right: 50%;
}

.offset-6_12 {
  margin-left: 50%;
}

.column-7_7 {
  width: 100%;
}

.column-7_8 {
  width: 87.5%;
}

.push-7_8 {
  left: 87.5%;
}

.pull-7_8 {
  right: 87.5%;
}

.offset-7_8 {
  margin-left: 87.5%;
}

.column-7_9 {
  width: 77.7777777778%;
}

.push-7_9 {
  left: 77.7777777778%;
}

.pull-7_9 {
  right: 77.7777777778%;
}

.offset-7_9 {
  margin-left: 77.7777777778%;
}

.column-7_10 {
  width: 70%;
}

.push-7_10 {
  left: 70%;
}

.pull-7_10 {
  right: 70%;
}

.offset-7_10 {
  margin-left: 70%;
}

.column-7_11 {
  width: 63.6363636364%;
}

.push-7_11 {
  left: 63.6363636364%;
}

.pull-7_11 {
  right: 63.6363636364%;
}

.offset-7_11 {
  margin-left: 63.6363636364%;
}

.column-7_12 {
  width: 58.3333333333%;
}

.push-7_12 {
  left: 58.3333333333%;
}

.pull-7_12 {
  right: 58.3333333333%;
}

.offset-7_12 {
  margin-left: 58.3333333333%;
}

.column-8_8 {
  width: 100%;
}

.column-8_9 {
  width: 88.8888888889%;
}

.push-8_9 {
  left: 88.8888888889%;
}

.pull-8_9 {
  right: 88.8888888889%;
}

.offset-8_9 {
  margin-left: 88.8888888889%;
}

.column-8_10 {
  width: 80%;
}

.push-8_10 {
  left: 80%;
}

.pull-8_10 {
  right: 80%;
}

.offset-8_10 {
  margin-left: 80%;
}

.column-8_11 {
  width: 72.7272727273%;
}

.push-8_11 {
  left: 72.7272727273%;
}

.pull-8_11 {
  right: 72.7272727273%;
}

.offset-8_11 {
  margin-left: 72.7272727273%;
}

.column-8_12 {
  width: 66.6666666667%;
}

.push-8_12 {
  left: 66.6666666667%;
}

.pull-8_12 {
  right: 66.6666666667%;
}

.offset-8_12 {
  margin-left: 66.6666666667%;
}

.column-9_9 {
  width: 100%;
}

.column-9_10 {
  width: 90%;
}

.push-9_10 {
  left: 90%;
}

.pull-9_10 {
  right: 90%;
}

.offset-9_10 {
  margin-left: 90%;
}

.column-9_11 {
  width: 81.8181818182%;
}

.push-9_11 {
  left: 81.8181818182%;
}

.pull-9_11 {
  right: 81.8181818182%;
}

.offset-9_11 {
  margin-left: 81.8181818182%;
}

.column-9_12 {
  width: 75%;
}

.push-9_12 {
  left: 75%;
}

.pull-9_12 {
  right: 75%;
}

.offset-9_12 {
  margin-left: 75%;
}

.column-10_10 {
  width: 100%;
}

.column-10_11 {
  width: 90.9090909091%;
}

.push-10_11 {
  left: 90.9090909091%;
}

.pull-10_11 {
  right: 90.9090909091%;
}

.offset-10_11 {
  margin-left: 90.9090909091%;
}

.column-10_12 {
  width: 83.3333333333%;
}

.push-10_12 {
  left: 83.3333333333%;
}

.pull-10_12 {
  right: 83.3333333333%;
}

.offset-10_12 {
  margin-left: 83.3333333333%;
}

.column-11_11 {
  width: 100%;
}

.column-11_12 {
  width: 91.6666666667%;
}

.push-11_12 {
  left: 91.6666666667%;
}

.pull-11_12 {
  right: 91.6666666667%;
}

.offset-11_12 {
  margin-left: 91.6666666667%;
}

.column-12_12 {
  width: 100%;
}

/* Utils */
.clearfix:after,
.row:after {
  content: " ";
  clear: both;
  width: 100%;
  height: 0;
  display: block;
}

.center-block {
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.pull-right {
  float: right !important;
}

.pull-left {
  float: left !important;
}

.affix {
  position: fixed;
  -webkit-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.visible {
  visibility: visible;
}

.invisible {
  visibility: hidden;
}

.show {
  display: block !important;
}

.hide {
  display: none !important;
}

.hidden {
  display: none !important;
  visibility: hidden !important;
}

.text-hide {
  font-size: 0;
  line-height: 0;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0;
}

.elementra_loading,
.trx_addons_loading {
  background-image: url(../../../images/preloader.png) !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
}

.theme_button_close,
.trx_addons_button_close,
.review-form a.close,
#cancel-comment-reply-link {
  display: block;
  cursor: pointer;
  width: 3rem;
  height: 3rem;
  line-height: 3rem;
  text-align: center;
  position: absolute;
  z-index: 1;
  top: 0;
  right: 0;
  margin: 0 !important;
}

.review-form a.close,
#cancel-comment-reply-link {
  width: 1.8rem;
  height: 1.8rem;
  line-height: 1.8rem;
  text-align: center;
}

.mfp-close-icon,
.review-form a.close,
#cancel-comment-reply-link {
  text-indent: -300px;
  overflow: hidden !important;
  -webkit-transition: -webkit-transform 0.3s ease 0s;
  -ms-transition: -ms-transform 0.3s ease 0s;
  transition: transform 0.3s ease 0s;
  -webkit-transform-origin: 50% 50%;
  -ms-transform-origin: 50% 50%;
  transform-origin: 50% 50%;
}

.mfp-close-icon,
.theme_button_close_icon,
.trx_addons_button_close_icon {
  position: absolute;
  z-index: 2;
  top: 25%;
  left: 25%;
  width: 50%;
  height: 50%;
  line-height: 50%;
  text-align: center;
  -webkit-box-sizing: border-box;
  -ms-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transition: -webkit-transform 0.3s ease 0s;
  -ms-transition: -ms-transform 0.3s ease 0s;
  transition: transform 0.3s ease 0s;
  -webkit-transform-origin: 50% 50%;
  -ms-transform-origin: 50% 50%;
  transform-origin: 50% 50%;
}

.theme_button_close:hover .theme_button_close_icon,
.trx_addons_button_close:hover .trx_addons_button_close_icon,
.mfp-close:hover .mfp-close-icon,
.review-form a.close:hover,
#cancel-comment-reply-link:hover {
  -webkit-transform: rotate(-180deg);
  -ms-transform: rotate(-180deg);
  transform: rotate(-180deg);
}

.theme_button_close_icon:before,
.theme_button_close_icon:after,
.trx_addons_button_close_icon:before,
.trx_addons_button_close_icon:after,
.mfp-close-icon:before,
.mfp-close-icon:after,
.review-form a.close:before,
.review-form a.close:after,
#cancel-comment-reply-link:before,
#cancel-comment-reply-link:after {
  content: ' ';
  position: absolute;
  z-index: 1;
  top: 50%;
  left: 0;
  -webkit-transform-origin: 50% 50%;
  -ms-transform-origin: 50% 50%;
  transform-origin: 50% 50%;
  width: 100%;
  height: 0;
  -webkit-box-sizing: border-box;
  -ms-box-sizing: border-box;
  box-sizing: border-box;
  margin-top: -1px;
  border-top: 2px solid var(--theme-color-title);
  -webkit-transition: border-color 0.3s ease 0s;
  -ms-transition: border-color 0.3s ease 0s;
  transition: border-color 0.3s ease 0s;
}

.theme_button_close:hover .theme_button_close_icon:before, .theme_button_close:hover .theme_button_close_icon:after,
.theme_button_close:focus .theme_button_close_icon:before,
.theme_button_close:focus .theme_button_close_icon:after,
.trx_addons_button_close:focus .trx_addons_button_close_icon:before,
.trx_addons_button_close:focus .trx_addons_button_close_icon:after,
.trx_addons_button_close:hover .trx_addons_button_close_icon:before,
.trx_addons_button_close:hover .trx_addons_button_close_icon:after,
.mfp-close:focus .mfp-close-icon:before,
.mfp-close:focus .mfp-close-icon:after,
.mfp-close:hover .mfp-close-icon:before,
.mfp-close:hover .mfp-close-icon:after,
.review-form a.close:hover:before,
.review-form a.close:hover:after,
#cancel-comment-reply-link:hover:before,
#cancel-comment-reply-link:hover:after {
  border-color: var(--theme-color-link);
}

.theme_button_close_icon:before,
.trx_addons_button_close_icon:before,
.mfp-close .mfp-close-icon:before,
.review-form a.close:before,
#cancel-comment-reply-link:before {
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}

.theme_button_close_icon:after,
.trx_addons_button_close_icon:after,
.mfp-close .mfp-close-icon:after,
.review-form a.close:after,
#cancel-comment-reply-link:after {
  -webkit-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
}

/* 6. Page layouts
-------------------------------------------------------------- */
.page_wrap {
  min-height: 100vh;
  -webkit-box-sizing: border-box;
  -ms-box-sizing: border-box;
  box-sizing: border-box;
}

.page_wrap,
.content_wrap {
  margin: 0 auto;
}

body:where(.body_style_boxed) {
  background-color: var(--theme-color-bg_color_2);
  background-attachment: fixed;
  background-position: center top;
  background-repeat: no-repeat;
  background-size: cover;
}

body.body_style_boxed .page_wrap {
  width: var(--theme-var-page_boxed);
  max-width: 100%;
}

.page_content_wrap {
  padding-top: var(--theme-var-main_content_padding);
  padding-bottom: var(--theme-var-main_content_padding);
}

body.remove_margins .page_content_wrap {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.content_wrap,
.content_container {
  width: var(--theme-var-page);
  max-width: 100%;
  margin: 0 auto;
}

.content_wrap .content_wrap,
.content_wrap .content_container,
.content_container .content_wrap,
.content_container .content_container {
  width: 100%;
}

.content_wrap:after,
.content_wrap_fullscreen:after,
.content_container:after {
  content: " ";
  clear: both;
  width: 100%;
  height: 0;
  display: block;
}

body.body_style_fullwide .content_wrap {
  max-width: var(--theme-var-page_fullwide_max);
  margin: 0 auto;
  width: 100% !important;
  padding-left: var(--theme-var-page_fullwide_extra);
  padding-right: var(--theme-var-page_fullwide_extra);
  -webkit-box-sizing: border-box;
  -ms-box-sizing: border-box;
  box-sizing: border-box;
}

.content,
.sidebar,
.sidebar_inner {
  -webkit-box-sizing: border-box;
  -ms-box-sizing: border-box;
  box-sizing: border-box;
}

.page_content_wrap .content_wrap,
.page_content_wrap .content_wrap_fullscreen {
  position: relative;
}

#page_preloader,
body.custom-background .content_wrap > .content {
  background-color: var(--theme-color-bg_color);
}

body.with_bg_canvas .page_content_wrap,
body.body_style_boxed.with_bg_canvas .page_wrap {
  background-color: transparent;
}

.preloader_wrap > div {
  background-color: var(--theme-color-link);
}

/* Content and Sidebar */
body.body_style_wide:not(.expand_content) [class*="content_wrap"] > .content,
body.body_style_boxed:not(.expand_content) [class*="content_wrap"] > .content {
  width: var(--theme-var-content);
}

[class*="content_wrap"] > .sidebar {
  width: var(--theme-var-sidebar);
}

body.sidebar_hide [class*="content_wrap"] > .content,
.previous_post_content.sidebar_hide [class*="content_wrap"] > .content {
  float: none;
  margin-left: auto;
  margin-right: auto;
}

body.sidebar_right [class*="content_wrap"] > .content {
  float: left;
}
body.sidebar_right [class*="content_wrap"] > .sidebar {
  float: right;
}

body.sidebar_left [class*="content_wrap"] > .content {
  float: right;
}
body.sidebar_left [class*="content_wrap"] > .sidebar {
  float: left;
}

/* Fullwide or Fullscreen with sidebar */
body.body_style_fullwide [class*="content_wrap"] > .content,
body.body_style_fullscreen [class*="content_wrap"] > .content {
  width: 100%;
}

body.body_style_fullwide.sidebar_right [class*="content_wrap"] > .content,
body.body_style_fullscreen.sidebar_right [class*="content_wrap"] > .content {
  padding-right: var(--theme-var-sidebar_and_gap);
}

body.body_style_fullwide.sidebar_right [class*="content_wrap"] > .sidebar,
body.body_style_fullscreen.sidebar_right [class*="content_wrap"] > .sidebar {
  margin-left: calc( -1 * var(--theme-var-sidebar) );
}

body.body_style_fullwide.sidebar_left [class*="content_wrap"] > .content,
body.body_style_fullscreen.sidebar_left [class*="content_wrap"] > .content {
  padding-left: var(--theme-var-sidebar_and_gap);
}

body.body_style_fullwide.sidebar_left [class*="content_wrap"] > .sidebar,
body.body_style_fullscreen.sidebar_left [class*="content_wrap"] > .sidebar {
  margin-right: calc( -1 * var(--theme-var-sidebar) );
}

/* Position */
/* This style is used in the plugin
body.body_style_fullscreen .content_wrap_fullscreen {
	overflow: clip;
}
*/
body.body_style_fullscreen .page_content_wrap {
  position: relative;
}

body.body_style_fullscreen [class*="content_wrap"] > .content > article.page {
  padding: 0;
}

/* Sticky sidebar */
body.fixed_blocks_sticky .sidebar:not(.elementor-element) {
  -webkit-position: sticky;
  position: -webkit-sticky;
  position: sticky;
  top: var(--fixed-rows-height);
  -webkit-transition: top 0.3s ease 0s;
  -ms-transition: top 0.3s ease 0s;
  transition: top 0.3s ease 0s;
  will-change: top;
}

.sidebar_fixed_placeholder {
  min-height: 1px;
}

/* 7. Section's decorations
=============================================================== */
/* 7.1 Header
-------------------------------------------------------------- */
/* Top panel */
.top_panel {
  position: relative;
  z-index: 8000;
  padding: 0.02px 0;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

.top_panel p {
  margin-bottom: 0;
}
.top_panel .row > [class*="column-"],
.top_panel .columns_wrap > [class*="column-"] {
  vertical-align: middle;
}

.top_panel.with_bg_image:before {
  content: '';
  position: absolute;
  z-index: -1;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.top_panel_default .top_panel_navi {
  background-color: var(--theme-color-bg_color);
}

/* Background video in the header */
#background_video {
  object-fit: cover;
  overflow: hidden;
  position: absolute;
  z-index: -1 !important;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
}

div#background_video {
  position: absolute !important;
}

div#background_video:after {
  content: ' ';
  position: absolute;
  z-index: 2;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.3);
}

div#background_video iframe,
div#background_video video {
  position: absolute;
  z-index: 1;
  top: 50% !important;
  left: 50% !important;
  -webkit-transform: translate(-50%, 50%) !important;
  -ms-transform: translate(-50%, 50%) !important;
  transform: translate(-50%, 50%) !important;
  max-width: none;
}

#tubular-container {
  display: none;
}

.top_panel.with_bg_video {
  background: #000;
}

/* Header positions - over */
.header_position_over .page_wrap {
  position: relative;
}

.header_position_over .top_panel {
  position: absolute;
  z-index: 8000;
  top: 0;
  left: 0;
  width: 100%;
  background-color: transparent;
}

/* Default header layouts
---------------------------------------------- */
/* Main menu in the default header */
.top_panel_default .sc_layouts_menu_nav > li.menu-item-has-children > a:after {
  content: '\e828';
  font-family: "fontello";
}
.top_panel_default .sc_layouts_menu_nav li li.menu-item-has-children > a:after {
  content: '\e836';
  font-family: "fontello";
}
.top_panel_default .sc_layouts_menu_mobile_button .sc_layouts_item_icon:before {
  content: '\e8ba';
  font-family: "fontello";
}

/* Blog/Page title */
.top_panel_default .top_panel_title {
  padding: var(--theme-var-main_content_padding) 0;
}
.top_panel_default .top_panel_title .sc_layouts_item {
  margin: 0 !important;
}

body:not(.custom-background) .top_panel_default:not(.with_bg_image) .top_panel_title {
  padding-bottom: 0;
}

/* Vertical menus */
.sc_layouts_menu_dir_vertical .sc_layouts_menu_nav li.menu-item-has-children > a > .open_child_menu {
  display: none;
  position: absolute;
  z-index: 2;
  top: 0;
  right: 0;
  bottom: 0;
  width: 2.2em;
}

/* Custom layouts
--------------------------------- */
.sc_layouts_row_delimiter {
  border-color: var(--theme-color-bd_color);
}

:where(body.trx_addons_page_scrolled) .sc_layouts_row_fixed_on {
  background-color: var(--theme-color-bg_color);
}

/* Row type: Narrow */
.sc_layouts_row.sc_layouts_row_type_narrow {
  background-color: var(--theme-color-bg_color_2);
}

/* Logo */
.sc_layouts_logo b {
  color: var(--theme-color-title);
}
.sc_layouts_logo i {
  color: var(--theme-color-link);
}

.sc_layouts_logo_text,
.sc_layouts_logo .logo_text {
  color: var(--theme-color-title);
}

.sc_layouts_logo_text:hover,
.sc_layouts_logo:hover .logo_text {
  color: var(--theme-color-link);
}

.sc_layouts_logo_slogan,
.sc_layouts_logo .logo_slogan {
  margin-top: 5px;
  color: var(--theme-color-text);
}

/* Page title and breadcrumbs */
.sc_layouts_title .sc_layouts_title_caption {
  margin: 0;
}
.sc_layouts_title * + .sc_layouts_title_breadcrumbs {
  margin-top: 0.5em;
}
.sc_layouts_title .sc_layouts_title_meta,
.sc_layouts_title .sc_layouts_title_breadcrumbs,
.sc_layouts_title .sc_layouts_title_breadcrumbs a,
.sc_layouts_title .sc_layouts_title_description,
.sc_layouts_title .post_meta,
.sc_layouts_title .post_meta_item,
.sc_layouts_title .post_meta_item a,
.sc_layouts_title .post_meta_item:after,
.sc_layouts_title .post_meta_item:hover:after,
.sc_layouts_title .post_meta_item.post_meta_edit:after,
.sc_layouts_title .post_meta_item.post_meta_edit:hover:after,
.sc_layouts_title .post_meta_item.post_categories,
.sc_layouts_title .post_meta_item.post_categories a,
.sc_layouts_title .post_info .post_info_item,
.sc_layouts_title .post_info .post_info_item a,
.sc_layouts_title .post_info_counters .post_meta_item {
  color: var(--theme-color-title);
}
.sc_layouts_title .post_meta_item a:hover,
.sc_layouts_title .post_meta_item a:focus,
.sc_layouts_title .sc_layouts_title_breadcrumbs a:hover,
.sc_layouts_title .sc_layouts_title_breadcrumbs a:focus,
.sc_layouts_title a.post_meta_item:hover,
.sc_layouts_title a.post_meta_item:focus,
.sc_layouts_title .post_meta_item.post_categories a:hover,
.sc_layouts_title .post_meta_item.post_categories a:focus,
.sc_layouts_title .post_info .post_info_item a:hover,
.sc_layouts_title .post_info .post_info_item a:focus,
.sc_layouts_title .post_info_counters .post_meta_item:hover,
.sc_layouts_title .post_info_counters .post_meta_item:focus {
  color: var(--theme-color-hover);
}

/* Menu
--------------------------------- */
.sc_layouts_menu_nav > li > a {
  color: var(--theme-color-title);
}

.sc_layouts_menu_nav > li > a:hover,
.sc_layouts_menu_nav > li.sfHover > a {
  color: var(--theme-color-link) !important;
}

.sc_layouts_menu_nav > li.current-menu-item > a,
.sc_layouts_menu_nav > li.current-menu-parent > a,
.sc_layouts_menu_nav > li.current-menu-ancestor > a {
  color: var(--theme-color-link) !important;
}

.sc_layouts_menu_nav .menu-collapse > a:before {
  color: var(--theme-color-text);
}
.sc_layouts_menu_nav .menu-collapse > a:after {
  background-color: var(--theme-color-bg_color_2);
}
.sc_layouts_menu_nav .menu-collapse > a:hover:before,
.sc_layouts_menu_nav .menu-collapse > a:focus:before {
  color: var(--theme-color-link);
}

/* Submenu */
.sc_layouts_menu_popup .sc_layouts_menu_nav,
.sc_layouts_menu_popup .sc_layouts_menu_nav > li > ul,
.sc_layouts_menu_nav > li > ul ul,
.sc_layouts_menu_nav > li ul:not(.sc_item_filters_tabs) {
  background-color: var(--theme-color-bg_color_2);
}

.sc_layouts_menu_popup .sc_layouts_menu_nav,
.sc_layouts_menu_nav > li ul:not(.sc_item_filters_tabs) {
  -webkit-box-shadow: none;
  -ms-box-shadow: none;
  box-shadow: none;
}

.widget_nav_menu li.menu-delimiter,
.sc_layouts_menu_nav > li li.menu-delimiter {
  border-color: var(--theme-color-bd_color);
}

.sc_layouts_menu_popup .sc_layouts_menu_nav > li > a,
.sc_layouts_menu_nav > li li > a {
  color: var(--theme-color-title) !important;
}

.sc_layouts_menu_popup .sc_layouts_menu_nav > li > a:hover,
.sc_layouts_menu_popup .sc_layouts_menu_nav > li.sfHover > a,
.sc_layouts_menu_nav > li li > a:hover,
.sc_layouts_menu_nav > li li.sfHover > a {
  color: var(--theme-color-link) !important;
  background-color: var(--theme-color-bg_color_2);
}

.sc_layouts_menu_nav > li li > a:hover:after {
  color: var(--theme-color-link) !important;
}

.sc_layouts_menu_nav li[class*="columns-"] li.menu-item-has-children > a:hover,
.sc_layouts_menu_nav li[class*="columns-"] li.menu-item-has-children.sfHover > a {
  color: var(--theme-color-text) !important;
  background-color: transparent;
}

.sc_layouts_menu_nav > li li[class*="icon-"]:before {
  color: var(--theme-color-link);
}

.sc_layouts_menu_nav > li li[class*="icon-"]:hover:before,
.sc_layouts_menu_nav > li li[class*="icon-"].shHover:before {
  color: var(--theme-color-link);
}

.sc_layouts_menu_nav > li li.current-menu-item > a,
.sc_layouts_menu_nav > li li.current-menu-parent > a,
.sc_layouts_menu_nav > li li.current-menu-ancestor > a {
  color: var(--theme-color-link) !important;
}

.sc_layouts_menu_nav > li li.current-menu-item:before,
.sc_layouts_menu_nav > li li.current-menu-parent:before,
.sc_layouts_menu_nav > li li.current-menu-ancestor:before {
  color: var(--theme-color-link) !important;
}

body.body_style_fullwide .sc_layouts_menu_nav > li[class*="columns-"] > ul {
  padding-left: var(--theme-var-page_fullwide_extra);
  padding-right: var(--theme-var-page_fullwide_extra);
}

/* Description in the menu */
.sc_layouts_menu_item_description {
  color: var(--theme-color-text);
}

.menu_main_nav > li ul [class*="current-menu-"] > a .sc_layouts_menu_item_description,
.sc_layouts_menu_nav > li ul li[class*="current-menu-"] > a .sc_layouts_menu_item_description,
.menu_main_nav > li ul a:hover .sc_layouts_menu_item_description,
.sc_layouts_menu_nav > li ul a:hover .sc_layouts_menu_item_description {
  color: var(--theme-color-meta);
}

.menu_main_nav > li[class*="current-menu-"] > a .sc_layouts_menu_item_description,
.sc_layouts_menu_nav > li[class*="current-menu-"] > a .sc_layouts_menu_item_description,
.menu_main_nav > li > a:hover .sc_layouts_menu_item_description,
.sc_layouts_menu_nav > li > a:hover .sc_layouts_menu_item_description {
  color: var(--theme-color-text);
}

/* Menu hovers
----------------------------------------- */
/* fade box */
.menu_hover_fade_box .sc_layouts_menu_nav > li > a {
  -webkit-border-radius: var(--theme-font-submenu_border-radius, 0);
  -ms-border-radius: var(--theme-font-submenu_border-radius, 0);
  border-radius: var(--theme-font-submenu_border-radius, 0);
}
.menu_hover_fade_box .sc_layouts_menu_nav > a:hover,
.menu_hover_fade_box .sc_layouts_menu_nav > li:not(.menu-collapse) > a:hover,
.menu_hover_fade_box .sc_layouts_menu_nav > li:not(.menu-collapse).sfHover > a {
  background-color: var(--theme-color-bg_color_2);
}

/* slide_box */
.menu_hover_slide_box .sc_layouts_menu_nav > li#blob {
  background-color: var(--theme-color-bg_color_2);
  -webkit-border-radius: var(--theme-font-submenu_border-radius, 0);
  -ms-border-radius: var(--theme-font-submenu_border-radius, 0);
  border-radius: var(--theme-font-submenu_border-radius, 0);
}

/* slide_line */
.menu_hover_slide_line .sc_layouts_menu_nav > li#blob {
  background-color: var(--theme-color-link);
}

/* color_line */
.menu_hover_color_line .sc_layouts_menu_nav > li:not(.menu-collapse) > a:before {
  background-color: var(--theme-color-title);
}
.menu_hover_color_line .sc_layouts_menu_nav > li:not(.menu-collapse) > a:after,
.menu_hover_color_line .sc_layouts_menu_nav > li:not(.menu-collapse).menu-item-has-children > a:after {
  background-color: var(--theme-color-link);
}
.menu_hover_color_line .sc_layouts_menu_nav > li:not(.menu-collapse).sfHover > a,
.menu_hover_color_line .sc_layouts_menu_nav > li:not(.menu-collapse) > a:hover,
.menu_hover_color_line .sc_layouts_menu_nav > li:not(.menu-collapse) > a:focus {
  color: var(--theme-color-link);
}

/* zoom_line */
.menu_hover_zoom_line .sc_layouts_menu_nav > li:not(.menu-collapse) > a:before {
  background-color: var(--theme-color-link);
}

/* path_line */
.menu_hover_path_line .sc_layouts_menu_nav > li:not(.menu-collapse):before,
.menu_hover_path_line .sc_layouts_menu_nav > li:not(.menu-collapse):after,
.menu_hover_path_line .sc_layouts_menu_nav > li:not(.menu-collapse) > a:before,
.menu_hover_path_line .sc_layouts_menu_nav > li:not(.menu-collapse) > a:after {
  background-color: var(--theme-color-link);
}

/* roll_down */
.menu_hover_roll_down .sc_layouts_menu_nav > li:not(.menu-collapse) > a:before {
  background-color: var(--theme-color-link);
}

/* Mobile menu
---------------------------------------- */
.menu_mobile_overlay {
  display: none !important;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.1);
  z-index: 100000;
}

.menu_mobile {
  position: fixed;
  z-index: 100002;
  top: 0;
  left: 0;
  width: 100%;
  height: 0;
  -webkit-transition: height 0.3s ease 0s;
  -ms-transition: height 0.3s ease 0s;
  transition: height 0.3s ease 0s;
}
.menu_mobile.opened {
  height: 100%;
}

.admin-bar .menu_mobile {
  top: 32px;
}
.admin-bar .menu_mobile.opened {
  height: calc(100% - 32px);
}

@media (max-width: 782px) {
  .admin-bar .menu_mobile {
    top: 46px;
  }
  .admin-bar .menu_mobile.opened {
    height: calc(100% - 46px);
  }
}
@media (max-width: 600px) {
  .admin-bar .menu_mobile {
    top: 0;
  }
  .admin-bar .menu_mobile.opened {
    height: 100%;
  }
}
.menu_mobile_inner {
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  text-align: left;
  overflow: hidden;
  overflow-y: auto;
  scrollbar-width: none;
  color: var(--theme-color-text);
  background-color: var(--theme-color-bg_color);
}

.menu_mobile .menu_mobile_nav_area {
  margin: 3em auto;
  width: var(--theme-var-page);
}

.menu_mobile:not(.opened) .theme_button_close_icon {
  -webkit-transform: scale(0.2) rotate(-45deg);
  -ms-transform: scale(0.2) rotate(-45deg);
  transform: scale(0.2) rotate(-45deg);
}

.menu_mobile_inner a,
.menu_mobile_inner .menu_mobile_nav_area li:before {
  color: var(--theme-color-title);
}

.menu_mobile_inner a:hover,
.menu_mobile_inner .current-menu-ancestor > a,
.menu_mobile_inner .current-menu-item > a,
.menu_mobile_inner .menu_mobile_nav_area li:hover:before,
.menu_mobile_inner .menu_mobile_nav_area li.current-menu-ancestor:before,
.menu_mobile_inner .menu_mobile_nav_area li.current-menu-item:before {
  color: var(--theme-color-link);
}

.menu_mobile .menu_mobile_nav_area ul {
  margin: 0 auto;
  padding: 0;
  list-style: none;
  width: auto;
}
.menu_mobile .menu_mobile_nav_area ul ul {
  margin: 0;
  display: none;
  padding: 0.6em 0;
}

.menu_mobile li > ul.sc_layouts_submenu ul {
  display: block;
}

.menu_mobile .menu_mobile_nav_area li {
  margin-bottom: 0;
  width: auto;
}

.menu_mobile .menu_mobile_nav_area .menu-item > a {
  padding: 0.25em 2em 0.25em 0;
  position: relative;
  display: inline-block;
  -webkit-box-sizing: border-box;
  -ms-box-sizing: border-box;
  box-sizing: border-box;
}

.menu_mobile .menu_mobile_nav_area > ul {
  width: 100%;
}

.menu_mobile .menu_mobile_nav_area .menu-item[class*="icon-"] {
  position: relative;
}
.menu_mobile .menu_mobile_nav_area .menu-item[class*="icon-"]:before {
  display: inline-block;
  padding: 0;
  font-size: var(--theme-font-h2_font-size);
  line-height: var(--theme-font-h2_line-height) !important;
  width: 1em;
  position: absolute;
  z-index: 1;
  top: 0.25em;
  left: 0;
  -webkit-transition: color 0.3s ease;
  -ms-transition: color 0.3s ease;
  transition: color 0.3s ease;
}

.menu_mobile .menu_mobile_nav_area > ul > .menu-item[class*="icon-"] > a {
  padding-left: 1.5em;
}

.menu_mobile .menu_mobile_nav_area > ul > .menu-item[class*="columns-"][class*="icon-"]:before {
  position: static;
  margin: 0 0 0 0.5em;
  float: left;
}

.menu_mobile .menu_mobile_nav_area > ul > .menu-item[class*="columns-"][class*="icon-"] > a {
  float: left;
  margin-left: -1.5em;
}

.menu_mobile .menu_mobile_nav_area > ul > .menu-item .menu-item > a {
  padding-left: 1em;
  padding-top: 0.25em;
  padding-bottom: 0.25em;
}

.menu_mobile .menu_mobile_nav_area > ul > .menu-item .menu-item[class*="icon-"]:before {
  font-size: var(--theme-font-submenu_font-size);
  line-height: var(--theme-font-submenu_line-height) !important;
  margin-left: 1em;
  top: 0.25em;
}

.menu_mobile .menu_mobile_nav_area > ul > .menu-item .menu-item[class*="icon-"] > a {
  padding-left: 3em;
}

.menu_mobile .menu_mobile_nav_area > ul > .menu-item .menu-item .menu-item > a {
  padding-left: 2em;
}

.menu_mobile .menu_mobile_nav_area > ul > .menu-item .menu-item .menu-item[class*="icon-"] > a {
  padding-left: 4em;
}

.menu_mobile .menu_mobile_nav_area > ul > .menu-item .menu-item .menu-item[class*="icon-"]:before {
  margin-left: 2em;
}

.menu_mobile .menu_mobile_nav_area > ul > .menu-item .menu-item .menu-item .menu-item > a {
  padding-left: 3em;
}

.menu_mobile .menu_mobile_nav_area > ul > .menu-item .menu-item .menu-item .menu-item[class*="icon-"] > a {
  padding-left: 5em;
}

.menu_mobile .menu_mobile_nav_area > ul > .menu-item .menu-item .menu-item .menu-item[class*="icon-"]:before {
  margin-left: 3em;
}

.menu_mobile .menu_mobile_nav_area .open_child_menu:before {
  font-family: "fontello";
  content: '\e828';
}

.menu_mobile .menu_mobile_nav_area .menu-item.opened > a > .open_child_menu:before {
  content: '\e835';
}

.menu_mobile .menu_mobile_nav_area .open_child_menu {
  display: block;
  position: absolute;
  z-index: 1;
  top: 0;
  right: 0;
  -webkit-box-sizing: content-box;
  -ms-box-sizing: content-box;
  box-sizing: content-box;
  text-align: center;
  width: 1em;
  line-height: var(--theme-font-h2_line-height) !important;
  padding: 0.25em;
  background-color: transparent;
  -webkit-transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
  -ms-transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
}

.menu_mobile .menu_mobile_nav_area > ul > .menu-item .menu-item.menu-delimiter {
  margin-top: 0 !important;
  padding-top: 0 !important;
}
.menu_mobile .menu_mobile_nav_area > ul > .menu-item .menu-item.menu-delimiter > a {
  overflow: hidden;
  padding-right: 0.5em;
  height: 1px;
  width: 100%;
}
.menu_mobile .menu_mobile_nav_area > ul > .menu-item .menu-item.menu-delimiter > a:before {
  content: ' ';
  display: block;
  border-top: 1px solid var(--theme-color-bd_color);
  width: 100%;
  height: 0;
}
.menu_mobile .menu_mobile_nav_area > ul > .menu-item .menu-item.menu-delimiter > a > * {
  display: none;
}

.menu_mobile .menu_mobile_nav_area ul.sc_layouts_submenu {
  display: none;
  padding: 1em 0;
  margin: 0 auto;
}
.menu_mobile .menu_mobile_nav_area ul.sc_layouts_submenu .sc_content {
  width: 100% !important;
}

.menu_mobile .menu_mobile_nav_area ul.sc_layouts_submenu .columns_wrap [class*="column-"],
.menu_mobile .menu_mobile_nav_area ul.sc_layouts_submenu .wpb_column:not([class*="vc_col-xs-"]),
.menu_mobile .menu_mobile_nav_area ul.sc_layouts_submenu .elementor-column:not([class*="elementor-xs-"]) {
  width: 100%;
  float: none;
}

.menu_mobile .menu_mobile_nav_area ul.sc_layouts_submenu .wpb_column:not([class*="vc_col-xs-"]) + .wpb_column:not([class*="vc_col-xs-"]),
.menu_mobile .menu_mobile_nav_area ul.sc_layouts_submenu .elementor-column:not([class*="elementor-xs-"]) + .elementor-column:not([class*="elementor-xs-"]) {
  margin-top: 2em;
}

/* 7.2 Post info (page/post title, category or tag name, author, meta, etc.)
-------------------------------------------------------------- */
/* Common styles */
.blog_archive {
  padding-bottom: calc( var(--theme-var-main_content_padding) / 2 );
}

.post_item .post_title {
  margin: 0;
}
.post_item .post_title a:hover {
  color: var(--theme-color-text);
}
.post_item .more-link {
  margin-top: var(--theme-var-grid_gap);
}

/* Post with password */
.post-password-form input[type="password"] {
  display: block;
  margin-top: 0.5em;
}
.post-password-form input[type="submit"] {
  margin-top: 0.5em;
}

/* Post info block */
.post_meta,
.post_meta_item,
.post_meta_item:after,
.post_meta_item:hover:after,
.post_meta_item a,
.post_info .post_info_item,
.post_info .post_info_item a,
.post_info_counters .post_meta_item {
  color: var(--theme-color-meta);
}

.post_date a:hover, .post_date a:focus,
a.post_meta_item:hover, a.post_meta_item:focus,
.post_meta_item a:hover, .post_meta_item a:focus,
.post_info .post_info_item a:hover, .post_info .post_info_item a:focus,
.post_info_meta .post_meta_item:hover, .post_info_meta .post_meta_item:focus {
  color: var(--theme-color-title);
}

.post_meta .post_meta_item {
  display: inline-block;
  margin-left: 0;
  -webkit-transition: color 0.3s ease 0s;
  -ms-transition: color 0.3s ease 0s;
  transition: color 0.3s ease 0s;
}
.post_meta .post_meta_item:after, .post_meta .post_meta_item.post_edit:after {
  content: '\e83c';
  font-family: "fontello";
  display: inline-block;
  vertical-align: baseline;
  font-size: inherit;
  font-weight: 400;
  font-style: normal;
  margin: 0 0.5em;
}
.post_meta .post_meta_item:last-child:after, .post_meta .post_meta_item.post_edit:last-child:after {
  display: none;
}
.post_meta .post_meta_item .post_author_by,
.post_meta .post_meta_item .post_author_avatar {
  display: none;
}
.post_meta .post_meta_item .post_meta_number + .post_meta_label {
  margin-left: 0.3em;
}
.post_meta a.post_meta_item:before,
.post_meta a.post_meta_item > .post_counters_number {
  margin-right: 0.3em;
}
.post_meta .post_meta_item_label {
  margin-right: 0.3em;
}

.post_meta_item.post_categories a {
  color: var(--theme-color-title);
}
.post_meta_item.post_categories a:hover {
  color: var(--theme-color-title);
}
.post_meta_item.post_categories > a + a {
  margin-left: 0.3em;
}

.post_meta .post_meta_item:before,
.socials_share .socials_caption:before {
  display: none;
}

/* Post's background */
.page_content_wrap {
  position: relative;
}

.custom-background .page_content_wrap {
  background-color: transparent !important;
}

.custom-background:not(.body_style_boxed) .content_wrap > .content {
  padding: calc(var(--theme-var-main_content_padding) / 3);
  -webkit-box-sizing: border-box;
  -ms-box-sizing: border-box;
  box-sizing: border-box;
}

/* Post featured block */
.post_featured {
  overflow: hidden;
  position: relative;
  margin-bottom: 1.8em;
  -webkit-border-radius: var(--theme-var-global-border-radius, 0);
  -ms-border-radius: var(--theme-var-global-border-radius, 0);
  border-radius: var(--theme-var-global-border-radius, 0);
}
.post_featured > p {
  margin: 0;
  height: 0;
}

.post_featured.with_thumb img {
  position: relative;
  z-index: 2;
}
.post_featured.with_thumb .mask {
  z-index: 3;
  background-color: rgba(0, 0, 0, 0.4);
  opacity: 0;
}
.post_featured.with_thumb:hover .mask {
  opacity: 1;
}

body.sidebar_hide .post_featured.alignwide,
body.sidebar_hide .post_featured.alignfull {
  z-index: 100;
}

.post_featured.with_gallery {
  overflow: visible;
}

.post_featured.with_audio .post_info_audio {
  text-align: left;
}

.post_featured_bg {
  position: relative;
}
.post_featured_bg:before {
  content: ' ';
  width: 0;
  height: 0;
  padding-top: 56.25%;
  display: inline-block;
  vertical-align: top;
  margin-left: -0.3em;
}
.post_featured_bg > .post_featured_bg_image, .post_featured_bg > .post_thumb {
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

.post_featured_right {
  float: right;
  width: 50%;
  margin-left: 4.3478%;
}

.post_featured_left {
  float: left;
  width: 50%;
  margin-right: 4.3478%;
}

.post_featured .mask {
  background-color: rgba(0, 0, 0, 0.5);
}

/* 7.3 Post Formats
-------------------------------------------------------------- */
/* Sticky posts */
.sticky {
  position: relative;
}
.sticky:not(.post_layout_custom) {
  border: 1px solid var(--theme-color-bd_color);
  padding: var(--theme-var-grid_gap);
}
.sticky:not(.post_layout_custom) .label_sticky {
  display: block;
  position: absolute;
  z-index: 1;
  top: -13px;
  right: -13px;
  width: 0;
  height: 0;
  border: 12px solid transparent;
  border-top-color: var(--theme-color-link);
  -webkit-transform: rotate(225deg);
  -ms-transform: rotate(225deg);
  transform: rotate(225deg);
}

ul.sc_layouts_submenu .sticky {
  border: none;
  padding: 0;
}
ul.sc_layouts_submenu .sticky .label_sticky {
  display: none;
}

/* Slider controls styles "format-gallery" */
.post_layout_classic.format-gallery .post_featured.with_thumb > img,
.related_wrap .format-gallery .post_featured.with_thumb > img {
  -webkit-transform: scale(0.998, 0.998);
  -ms-transform: scale(0.998, 0.998);
  transform: scale(0.998, 0.998);
}
.post_layout_classic.format-gallery .slider_container .swiper-pagination-bullets,
.related_wrap .format-gallery .slider_container .swiper-pagination-bullets {
  bottom: 1.5em;
}
.post_layout_classic.format-gallery .slider_container .swiper-pagination-bullets .swiper-pagination-bullet,
.related_wrap .format-gallery .slider_container .swiper-pagination-bullets .swiper-pagination-bullet {
  width: 7px;
  height: 7px;
  margin: 0 0.5rem;
  border: none;
  background-color: var(--theme-color-bg_color_07);
  opacity: 1;
}
.post_layout_classic.format-gallery .slider_container .swiper-pagination-bullets .swiper-pagination-bullet-active,
.related_wrap .format-gallery .slider_container .swiper-pagination-bullets .swiper-pagination-bullet-active {
  background-color: var(--theme-color-bg_color);
}
.post_layout_classic.format-gallery .slider_container .slider_controls_wrap > a,
.post_layout_classic.format-gallery .slider_container:hover .slider_controls_wrap > a,
.related_wrap .format-gallery .slider_container .slider_controls_wrap > a,
.related_wrap .format-gallery .slider_container:hover .slider_controls_wrap > a {
  opacity: 1;
  margin-left: 0;
  margin-right: 0;
}
.post_layout_classic.format-gallery .slider_container .slider_controls_wrap > a,
.related_wrap .format-gallery .slider_container .slider_controls_wrap > a {
  width: 2.1875rem;
  height: 3rem;
  line-height: 3rem;
  -webkit-border-radius: var(--theme-var-global-border-radius-small, 0);
  -ms-border-radius: var(--theme-var-global-border-radius-small, 0);
  border-radius: var(--theme-var-global-border-radius-small, 0);
}
.post_layout_classic.format-gallery .slider_container .slider_controls_wrap > a.slider_prev, .post_layout_classic.format-gallery .slider_container .slider_controls_wrap > a.slider_next,
.related_wrap .format-gallery .slider_container .slider_controls_wrap > a.slider_prev,
.related_wrap .format-gallery .slider_container .slider_controls_wrap > a.slider_next {
  margin-top: -1.5rem;
  color: var(--theme-color-title);
  background-color: var(--theme-color-bg_color);
}
.post_layout_classic.format-gallery .slider_container .slider_controls_wrap > a.slider_prev:hover, .post_layout_classic.format-gallery .slider_container .slider_controls_wrap > a.slider_next:hover,
.related_wrap .format-gallery .slider_container .slider_controls_wrap > a.slider_prev:hover,
.related_wrap .format-gallery .slider_container .slider_controls_wrap > a.slider_next:hover {
  color: var(--theme-color-text);
}
.post_layout_classic.format-gallery .slider_container .slider_controls_wrap > a.slider_prev,
.related_wrap .format-gallery .slider_container .slider_controls_wrap > a.slider_prev {
  left: 1rem;
}
.post_layout_classic.format-gallery .slider_container .slider_controls_wrap > a.slider_next,
.related_wrap .format-gallery .slider_container .slider_controls_wrap > a.slider_next {
  right: 1rem;
}

/* Media elements
-------------------------------------- */
.me-plugin {
  position: absolute;
  width: 0;
  height: 0;
}

.mejs-container * {
  font-family: var(--theme-font-p_font-family);
  font-weight: var(--theme-font-p_font-weight);
}

.mejs-time {
  font-size: 12px;
  line-height: 9px;
  overflow: visible;
}

.mejs-controls .mejs-time-rail .mejs-time-current {
  background: var(--theme-color-link);
}

.mejs-controls button {
  background-color: transparent;
  -webkit-border-radius: 0;
  -ms-border-radius: 0;
  border-radius: 0;
}

/* Audio */
.format-audio .post_featured .post_info {
  display: none !important;
}

.format-audio .post_featured.with_thumb:after {
  content: ' ';
  display: block;
  position: absolute;
  z-index: 9;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: auto;
  height: auto;
  padding: 0;
  margin: 0;
  background: -moz-linear-gradient(top, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.15) 50%, rgba(0, 0, 0, 0.25) 66%, rgba(0, 0, 0, 0.5) 100%);
  background: -webkit-linear-gradient(top, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.15) 50%, rgba(0, 0, 0, 0.25) 66%, rgba(0, 0, 0, 0.5) 100%);
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.15) 50%, rgba(0, 0, 0, 0.25) 66%, rgba(0, 0, 0, 0.5) 100%);
}

.post_featured .post_audio.with_iframe .post_audio_author, .post_featured .post_audio.with_iframe .post_audio_title {
  display: none;
}

.post_featured .post_audio_author {
  font-size: 0.9375em;
}
.post_featured .post_audio_title {
  margin-top: 5px;
}
.post_featured [class*="post_audio_"]:last-child {
  margin-bottom: 1.2rem;
}
.post_featured .post_audio_author {
  color: var(--theme-color-meta);
}
.post_featured.with_thumb .post_audio_author, .post_featured.with_thumb .post_audio_description, .post_featured.with_thumb .post_audio_title {
  color: #ffffff;
}
.post_featured.with_thumb .post_audio {
  position: absolute;
  z-index: 1000;
  top: auto;
  right: var(--theme-var-grid_gap);
  bottom: var(--theme-var-grid_gap);
  left: var(--theme-var-grid_gap);
  text-align: left;
}
.post_featured.without_thumb .post_audio {
  border: 1px solid var(--theme-color-bd_color);
  background-color: var(--theme-color-bg_color_2);
}
.post_featured.without_thumb .post_audio:not(.with_iframe) {
  padding: var(--theme-var-grid_gap);
}

/* Video */
.post_featured.with_thumb .post_video {
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  position: absolute;
  z-index: 2;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  -webkit-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  transition: all 0.3s ease;
  text-align: center;
  opacity: 0;
  overflow: hidden;
}

.trx_addons_video_player.with_cover .video_hover,
.post_featured.with_thumb .post_video_hover {
  width: 2.8em;
  height: 2.8em;
  line-height: 2.8em;
  text-align: center;
  -webkit-border-radius: 50%;
  -ms-border-radius: 50%;
  border-radius: 50%;
  -webkit-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  transition: all 0.3s ease;
  position: absolute;
  z-index: 2000;
  top: 50%;
  left: 50%;
  -webkit-transform: none;
  -ms-transform: none;
  transform: none;
  margin-left: -1.4em;
  font-size: 1.2em;
  opacity: 1;
  cursor: pointer;
  overflow: hidden;
  color: var(--theme-color-title);
  background-color: var(--theme-color-bg_color);
}
.trx_addons_video_player.with_cover .video_hover:before,
.post_featured.with_thumb .post_video_hover:before {
  content: '\E8E1';
  font-family: "fontello";
  letter-spacing: -1px;
  margin: 0;
}
.trx_addons_video_player.with_cover .video_hover:hover,
.post_featured.with_thumb .post_video_hover:hover {
  color: var(--theme-color-text);
  background-color: var(--theme-color-bg_color);
}

.trx_addons_video_player.with_cover .video_hover,
.post_featured.with_thumb .post_video_hover,
.trx_addons_video_player.with_cover:hover .video_hover,
.post_featured.with_thumb:hover .post_video_hover {
  margin-top: -1.4em;
}

.post_featured.with_thumb .post_video_hover > a {
  display: block;
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.trx_addons_video_player.with_cover:hover .video_mask {
  opacity: 0;
}

.post_featured.post_video_play .post_video {
  opacity: 1;
  z-index: 100;
}
.post_featured.post_video_play .mask {
  opacity: 1;
  background-color: #000;
}
.post_featured.post_video_play .post_video_hover {
  display: none;
}

.trx_addons_video_player.with_cover.video_play {
  background-color: #000;
}
.trx_addons_video_player.with_cover.video_play img {
  opacity: 0;
}

/* Gallery */
.format-gallery .post_featured.with_thumb .slider_outer {
  position: absolute;
  z-index: 1000;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  width: 100%;
}

/* Aside, Link, Status, Quote */
.format-quote .post_content,
.format-aside .post_content,
.format-link .post_content,
.format-status .post_content {
  padding: 0;
  text-align: left;
}

.format-aside .post_content_inner,
.format-link .post_content_inner,
.format-status .post_content_inner {
  font-size: 1.125rem;
}

.format-aside .post_content_inner {
  padding: var(--theme-var-grid_gap);
  color: var(--theme-color-title);
  background-color: var(--theme-color-bg_color_2);
}

.format-link .post_content_inner,
.format-status .post_content_inner {
  color: var(--theme-color-title);
}

/* 7.4 Paginations
-------------------------------------------------------------- */
/* Blog pagination: Load more and Infinite */
.nav-links-more {
  text-align: center;
  margin-top: 1.5em;
}
.nav-links-more .nav-load-more {
  position: relative;
  -webkit-appearance: none;
  cursor: pointer;
  display: inline-block;
  vertical-align: top;
  -webkit-box-sizing: border-box;
  -ms-box-sizing: border-box;
  box-sizing: border-box;
  /* white-space: nowrap; Allow button text to wrap */
  height: auto;
  max-width: 100%;
  color: var(--theme-font-button_color, var(--theme-color-bg_color));
  border-color: var(--theme-font-button_border-color, var(--theme-color-link));
  background-color: var(--theme-font-button_background-color, var(--theme-color-link));
}
.nav-links-more .nav-load-more:hover {
  color: var(--theme-font-button_color-hover, var(--theme-color-bg_color));
  border-color: var(--theme-font-button_border-color-hover, var(--theme-color-hover));
  background-color: var(--theme-font-button_background-color-hover, var(--theme-color-hover));
}
.nav-links-more .nav-load-more:before {
  content: '\E803';
  font-family: "fontello";
  font-size: 1.25rem;
  font-weight: 400;
  display: block;
  margin-left: -0.5em;
  margin-top: -0.5em;
  position: absolute;
  z-index: 1;
  top: 50%;
  left: 50%;
  width: 1em;
  height: 1em;
  line-height: 1em;
  text-align: center;
  opacity: 0;
}
.nav-links-more.loading span {
  opacity: 0;
}
.nav-links-more.loading .nav-load-more:before {
  opacity: 1;
  -webkit-animation: spin 2s infinite linear;
  -ms-animation: spin 2s infinite linear;
  animation: spin 2s infinite linear;
}
.nav-links-more.nav-links-infinite {
  display: none;
}
.nav-links-more.nav-links-infinite.loading {
  display: block;
}
.nav-links-more.nav-links-infinite .nav-load-more {
  padding: 0;
  width: 3.125rem;
  height: 3.125rem;
  line-height: 3.125rem;
  color: var(--theme-color-title);
  background-color: var(--theme-color-bd_color);
}
.nav-links-more.nav-links-infinite a span {
  display: none;
}

/* Blog pagination: Prev/Next links */
.nav-links-old {
  font-size: 0.875rem;
  line-height: 1.1875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.03rem;
  margin-top: 1.5em;
  overflow: hidden;
  color: var(--theme-color-title);
}
.nav-links-old a {
  color: var(--theme-color-title);
}
.nav-links-old a:hover {
  color: var(--theme-color-text);
}
.nav-links-old .nav-prev,
.nav-links-old .nav-next {
  position: relative;
}
.nav-links-old .nav-prev a:before,
.nav-links-old .nav-next a:after {
  font-family: "fontello";
  font-size: 1.2rem;
  font-weight: 400;
  display: inline;
}
.nav-links-old .nav-prev {
  float: left;
  padding-left: 1rem;
}
.nav-links-old .nav-prev a:before {
  content: '\E837';
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
}
.nav-links-old .nav-next {
  float: right;
  padding-right: 1rem;
}
.nav-links-old .nav-next a:after {
  content: '\E836';
  position: absolute;
  z-index: 1;
  top: 0;
  right: 0;
}

/* Blog pagination: Page numbers */
.comments_pagination,
.nav-links,
.page_links,
.wp-block-query-pagination {
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 6px;
  font-weight: 500;
  margin-top: 1.5em;
  list-style-type: none;
  clear: both;
}

.page_links {
  margin-top: 0;
}

.page_links > span:not(.page_links_title),
.page_links > a,
.comments_pagination .page-numbers,
.nav-links .page-numbers,
.wp-block-query-pagination .page-numbers,
.wp-block-query-pagination .wp-block-query-pagination-previous,
.wp-block-query-pagination .wp-block-query-pagination-next {
  display: inline-block;
  vertical-align: top;
  font-size: inherit;
  font-weight: inherit;
  padding: 0;
  border: 1px solid;
  text-align: center;
  width: 3.125rem;
  height: 3.125rem;
  line-height: 3rem;
  -webkit-box-sizing: border-box;
  -ms-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-border-radius: var(--theme-var-blog-pagination-border-radius, 0);
  -ms-border-radius: var(--theme-var-blog-pagination-border-radius, 0);
  border-radius: var(--theme-var-blog-pagination-border-radius, 0);
  -webkit-transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
  -ms-transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
}
.page_links > span:not(.page_links_title).dots,
.page_links > a.dots,
.comments_pagination .page-numbers.dots,
.nav-links .page-numbers.dots,
.wp-block-query-pagination .page-numbers.dots,
.wp-block-query-pagination .wp-block-query-pagination-previous.dots,
.wp-block-query-pagination .wp-block-query-pagination-next.dots {
  border: none;
  background: none;
  line-height: 2.375rem;
}

.page_links > a,
.comments_pagination .page-numbers,
.nav-links .page-numbers,
.wp-block-query-pagination .page-numbers,
.wp-block-query-pagination .wp-block-query-pagination-previous,
.wp-block-query-pagination .wp-block-query-pagination-next {
  color: var(--theme-color-title);
  border-color: var(--theme-color-bd_color);
  background-color: var(--theme-color-bg_color_2);
}

.page_links > span:not(.page_links_title),
.comments_pagination .page-numbers.current,
.nav-links .page-numbers.current,
.wp-block-query-pagination .page-numbers.current {
  color: var(--theme-color-alt_title);
  border-color: var(--theme-color-alt_bg_color);
  background-color: var(--theme-color-alt_bg_color);
}

.page_links > a:hover,
.comments_pagination a.page-numbers:hover,
.nav-links a.page-numbers:hover,
.wp-block-query-pagination a.page-numbers:hover,
.wp-block-query-pagination .wp-block-query-pagination-previous:hover,
.wp-block-query-pagination .wp-block-query-pagination-next:hover {
  color: var(--theme-color-title);
  border-color: var(--theme-color-bd_color);
  background-color: var(--theme-color-bg_color);
}

.nav-links .page-numbers.prev, .nav-links .page-numbers.next, .nav-links .page-numbers.wp-block-query-pagination-previous, .nav-links .page-numbers.wp-block-query-pagination-next,
.wp-block-query-pagination .page-numbers.prev,
.wp-block-query-pagination .page-numbers.next,
.wp-block-query-pagination .page-numbers.wp-block-query-pagination-previous,
.wp-block-query-pagination .page-numbers.wp-block-query-pagination-next,
.wp-block-query-pagination .wp-block-query-pagination-previous.prev,
.wp-block-query-pagination .wp-block-query-pagination-previous.next,
.wp-block-query-pagination .wp-block-query-pagination-previous.wp-block-query-pagination-previous,
.wp-block-query-pagination .wp-block-query-pagination-previous.wp-block-query-pagination-next,
.wp-block-query-pagination .wp-block-query-pagination-next.prev,
.wp-block-query-pagination .wp-block-query-pagination-next.next,
.wp-block-query-pagination .wp-block-query-pagination-next.wp-block-query-pagination-previous,
.wp-block-query-pagination .wp-block-query-pagination-next.wp-block-query-pagination-next,
.comments_pagination .page-numbers.prev,
.comments_pagination .page-numbers.next,
.comments_pagination .page-numbers.wp-block-query-pagination-previous,
.comments_pagination .page-numbers.wp-block-query-pagination-next {
  text-indent: -200px;
  overflow: hidden;
  position: relative;
}
.nav-links .page-numbers.prev:before, .nav-links .page-numbers.next:before, .nav-links .page-numbers.wp-block-query-pagination-previous:before, .nav-links .page-numbers.wp-block-query-pagination-next:before,
.wp-block-query-pagination .page-numbers.prev:before,
.wp-block-query-pagination .page-numbers.next:before,
.wp-block-query-pagination .page-numbers.wp-block-query-pagination-previous:before,
.wp-block-query-pagination .page-numbers.wp-block-query-pagination-next:before,
.wp-block-query-pagination .wp-block-query-pagination-previous.prev:before,
.wp-block-query-pagination .wp-block-query-pagination-previous.next:before,
.wp-block-query-pagination .wp-block-query-pagination-previous.wp-block-query-pagination-previous:before,
.wp-block-query-pagination .wp-block-query-pagination-previous.wp-block-query-pagination-next:before,
.wp-block-query-pagination .wp-block-query-pagination-next.prev:before,
.wp-block-query-pagination .wp-block-query-pagination-next.next:before,
.wp-block-query-pagination .wp-block-query-pagination-next.wp-block-query-pagination-previous:before,
.wp-block-query-pagination .wp-block-query-pagination-next.wp-block-query-pagination-next:before,
.comments_pagination .page-numbers.prev:before,
.comments_pagination .page-numbers.next:before,
.comments_pagination .page-numbers.wp-block-query-pagination-previous:before,
.comments_pagination .page-numbers.wp-block-query-pagination-next:before {
  font-family: "fontello";
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  width: 100%;
  text-align: center;
  text-indent: 0;
}

.nav-links .page-numbers.prev:before,
.wp-block-query-pagination .page-numbers.prev:before,
.wp-block-query-pagination .wp-block-query-pagination-previous:before,
.comments_pagination .page-numbers.prev:before {
  content: '\F007';
  font-size: 0.875rem;
  font-weight: 400;
  letter-spacing: 0.06rem;
}

.nav-links .page-numbers.next:before,
.wp-block-query-pagination .page-numbers.next:before,
.wp-block-query-pagination .wp-block-query-pagination-next:before,
.comments_pagination .page-numbers.next:before {
  content: '\F006';
  font-size: 0.875rem;
  font-weight: 400;
  letter-spacing: -0.06rem;
}

.wp-block-query-pagination > .wp-block-query-pagination-next,
.wp-block-query-pagination > .wp-block-query-pagination-numbers,
.wp-block-query-pagination > .wp-block-query-pagination-previous {
  margin: 0;
}

/* 8. General pages
=============================================================== */
/* 8.1 Page 404
-------------------------------------------------------------- */
.post_item_404 .post_content {
  text-align: center;
}
.post_item_404 .page_title {
  font-size: 17rem;
  line-height: 0.9;
  margin: 0;
  hyphens: none;
}
.post_item_404 .page_info {
  margin-top: 2.8rem;
}
.post_item_404 .page_subtitle {
  margin: 0;
}
.post_item_404 .page_description {
  margin-top: 1rem;
  margin-bottom: 2.5rem;
}

/* 8.2 Page 'No search results' and 'No archive results'
-------------------------------------------------------- */
.post_item_none_search .page_info,
.post_item_none_archive .page_info {
  width: 100%;
  max-width: 33.125rem;
  margin: 0 auto;
}
.post_item_none_search .search_wrap,
.post_item_none_archive .search_wrap {
  display: inline-block;
  width: 100%;
  max-width: 25.625rem;
}
body.sidebar_show .post_item_none_search .post_content, body.sidebar_show
.post_item_none_archive .post_content {
  text-align: left;
}
body.sidebar_show .post_item_none_search .post_content .page_info, body.sidebar_show
.post_item_none_archive .post_content .page_info {
  max-width: 100%;
  margin: 0;
}

/* 8.3 Author's page
------------------------------------------------------ */
.author_page {
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: calc(var(--theme-var-main_content_padding) / 2);
}
.author_page .author_avatar {
  -webkit-border-radius: var(--theme-var-profile-image-border-radius, 50%);
  -ms-border-radius: var(--theme-var-profile-image-border-radius, 50%);
  border-radius: var(--theme-var-profile-image-border-radius, 50%);
  margin-bottom: 1rem;
  overflow: hidden;
}
.author_page .author_title {
  margin-top: 0;
  margin-bottom: 0.5rem;
}
.author_page .author_bio {
  text-align: center;
  margin-bottom: 1rem;
}
.author_page .author_bio p {
  margin: 0;
}
.author_page .author_bio p + p {
  margin-top: 0.6rem;
}
.author_page .author_details {
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 0.4rem;
}
.author_page .author_details .author_posts_total,
.author_page .author_details .author_socials {
  text-transform: capitalize;
}
.author_page .author_details .author_posts_total_value {
  font-weight: bold;
  color: var(--theme-color-title);
}
.author_page .author_details .author_socials {
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 0.4rem;
}
.author_page .author_details .author_socials .author_socials_caption {
  display: none;
}
.author_page .author_details .author_socials .socials_wrap {
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-align-items: baseline;
  -ms-flex-align: baseline;
  align-items: baseline;
  gap: 1rem;
}
.author_page .author_details .author_socials .socials_wrap .social_item {
  color: var(--theme-color-title);
}
.author_page .author_details .author_socials .socials_wrap .social_item:hover {
  color: var(--theme-color-link);
}

/* 9. Sidebars
-------------------------------------------------------------- */
/* Common rules */
:root {
  --theme-var-sidebar_paddings: calc( var(--theme-var-sidebar) * 0.14 );
}

.sidebar_default .widget + .widget, .elementor-widget-sidebar .widget + .widget {
  margin-top: var(--theme-var-sidebar_paddings);
}

.widget p {
  margin: 0;
}
.widget p + p,
.widget p + div,
.widget p + form {
  margin-top: 1em;
}
.widget ul {
  margin: 0;
}
.widget .widget_title,
.widget .widgettitle {
  margin-top: 0;
  margin-bottom: 1em;
}

:where(.footer_default, .sidebar_default, .elementor-widget-sidebar) .widget > *:last-child,
:where(.footer_default, .sidebar_default, .elementor-widget-sidebar) .widget > .wp-block-group > *:last-child {
  margin-bottom: 0;
}

.wp-block-archives-list,
.wp-block-categories-list {
  padding-left: 1.2em;
  list-style-type: none;
}
.wp-block-archives-list > li,
.wp-block-categories-list > li {
  position: relative;
  overflow: visible;
}
.wp-block-archives-list > li:before,
.wp-block-categories-list > li:before {
  content: ' ';
  display: block;
  width: 4px;
  height: 4px;
  line-height: 4px;
  text-align: center;
  position: absolute;
  z-index: 1;
  top: 0.75em;
  left: -1.2em;
  -webkit-border-radius: 50%;
  -ms-border-radius: 50%;
  border-radius: 50%;
  background-color: var(--theme-color-title);
}

/* Widget: Latest Posts */
.wp-block-latest-posts__post-date, .wp-block-latest-posts__post-author {
  font-size: 0.875em;
  line-height: 1.45em;
}
.wp-block-latest-posts:not(.is-grid) li + li {
  margin-top: 0.8rem;
}

/* Widget: Latest Comments */
.wp-block-latest-comments {
  padding-left: 0;
}
.wp-block-latest-comments .avatar, .wp-block-latest-comments__comment-avatar {
  margin-top: 0.25em;
}
.wp-block-latest-comments__comment-date, .wp-block-latest-comments__comment-excerpt p {
  font-size: 0.875em;
  line-height: 1.45em;
}
.wp-block-latest-comments__comment-date {
  color: var(--theme-color-meta);
}

/* Widget: RSS */
.wp-block-rss__item-author, .wp-block-rss__item-publish-date {
  color: var(--theme-color-meta);
  font-size: 0.875em;
  line-height: 1.6em;
}
.wp-block-rss:not(.is-grid) li + li {
  margin-top: 0.8rem;
}

/* Widgets: WP Search - OLD */
.widget_search form.search-form {
  width: 100%;
  overflow: hidden;
  position: relative;
}
.widget_search form.search-form:after {
  content: '\e83a';
  font-family: "fontello";
  display: block;
  position: absolute;
  z-index: 0;
  top: 50%;
  left: 1.1rem;
  margin-top: -0.5rem;
  width: 1rem;
  height: 1rem;
  line-height: 1rem;
  text-align: center;
  pointer-events: none;
  cursor: pointer;
  -webkit-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  transition: all 0.3s ease;
  color: var(--theme-color-text);
}
.widget_search form.search-form:hover:after {
  color: var(--theme-color-title);
}
.widget_search form.search-form .search-field {
  width: 100%;
  padding-left: 2.6rem !important;
}
.widget_search form.search-form input.search-submit {
  display: block;
  overflow: hidden;
  text-indent: -1000px;
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  width: 2.6rem;
  height: 100%;
  padding: 0;
  border: none !important;
  background: none !important;
  -webkit-box-shadow: none;
  -ms-box-shadow: none;
  box-shadow: none;
}

/* Widgets: WP Search - NEW */
.wp-block-search .wp-block-search__label {
  margin-bottom: 8px;
  display: inline-block;
}
.wp-block-search input.wp-block-search__input {
  max-height: 50px;
  color: var(--theme-font-input_color, var(--theme-color-text));
  border-color: var(--theme-font-input_border-color, var(--theme-color-bd_color));
  background-color: var(--theme-font-input_background-color, var(--theme-color-bg_color));
}
.wp-block-search input.wp-block-search__input.filled, .wp-block-search input.wp-block-search__input:focus {
  color: var(--theme-font-input_color-focus, var(--theme-color-title));
  border-color: var(--theme-font-input_border-color-focus, var(--theme-color-meta));
  background-color: var(--theme-font-input_background-color-focus, var(--theme-color-bg_color));
}
.wp-block-search button.wp-block-search__button {
  -webkit-appearance: none;
  cursor: pointer;
  display: inline-block;
  vertical-align: top;
  -webkit-box-sizing: border-box;
  -ms-box-sizing: border-box;
  box-sizing: border-box;
  /* white-space: nowrap; Allow button text to wrap */
  height: auto;
  max-width: 100%;
  color: var(--theme-font-button_color, var(--theme-color-bg_color));
  border-color: var(--theme-font-button_border-color, var(--theme-color-link));
  background-color: var(--theme-font-button_background-color, var(--theme-color-link));
  padding: 5px 20px !important;
  -webkit-border-radius: var(--theme-font-input_border-radius, 0);
  -ms-border-radius: var(--theme-font-input_border-radius, 0);
  border-radius: var(--theme-font-input_border-radius, 0);
}
.wp-block-search button.wp-block-search__button:hover, .wp-block-search button.wp-block-search__button:focus {
  color: var(--theme-font-button_color-hover, var(--theme-color-bg_color));
  border-color: var(--theme-font-button_border-color-hover, var(--theme-color-hover));
  background-color: var(--theme-font-button_background-color-hover, var(--theme-color-hover));
}
.wp-block-search button.wp-block-search__button.has-icon {
  padding: 0 !important;
  min-width: 50px;
}
.wp-block-search.wp-block-search__button-only .wp-block-search__input {
  margin-right: 4px;
}
.wp-block-search.wp-block-search__button-inside .wp-block-search__inside-wrapper {
  border-color: var(--theme-font-input_border-color, var(--theme-color-bd_color));
  -webkit-border-radius: var(--theme-font-input_border-radius, 0);
  -ms-border-radius: var(--theme-font-input_border-radius, 0);
  border-radius: var(--theme-font-input_border-radius, 0);
  background: var(--theme-color-bg_color_2);
}
.wp-block-search.wp-block-search__button-inside .wp-block-search__inside-wrapper button.wp-block-search__button.has-icon {
  margin-left: 4px;
}

/* Widget: Calendar */
.wp-block-calendar caption {
  padding-bottom: 0.5em;
  margin: 0;
}
.wp-block-calendar caption, .wp-block-calendar th {
  color: var(--theme-color-title);
}
.wp-block-calendar th, .wp-block-calendar td {
  font-size: 1em;
  text-align: center;
  border: none !important;
  background-color: transparent !important;
}
.wp-block-calendar td#today {
  font-weight: bold;
}

/* Widgets: WP Tag Cloud */
.widget_product_tag_cloud,
.widget_tag_cloud {
  overflow: hidden;
}

.wp-block-tag-cloud,
.widget_product_tag_cloud .tagcloud,
.widget_tag_cloud .tagcloud,
.post_item_single .post_tags_single {
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 6px;
}

.wp-block-tag-cloud a,
.widget_product_tag_cloud a,
.widget_tag_cloud a,
.post_item_single .post_tags_single a {
  margin: 0;
  padding: 8px 20px;
  font-size: 13px;
  line-height: 17px;
  -webkit-border-radius: var(--theme-var-global-border-radius-small, 0);
  -ms-border-radius: var(--theme-var-global-border-radius-small, 0);
  border-radius: var(--theme-var-global-border-radius-small, 0);
  -webkit-transition: color 0.3s ease;
  -ms-transition: color 0.3s ease;
  transition: color 0.3s ease;
  color: var(--theme-color-meta);
  border: 1px solid var(--theme-color-bd_color);
  background-color: var(--theme-color-bg_color);
}
.wp-block-tag-cloud a:hover,
.widget_product_tag_cloud a:hover,
.widget_tag_cloud a:hover,
.post_item_single .post_tags_single a:hover {
  color: var(--theme-color-title);
}

.wp-block-tag-cloud[style*="line-height:"] a {
  line-height: inherit;
}

/* Widgets: WP Block Archives */
.wp-block-archives-dropdown label {
  margin-bottom: 8px;
}

/* WP Block Comments */
.wp-block-comments .wp-block-columns {
  gap: 0;
  margin-bottom: 1.5em;
}
.wp-block-comments .wp-block-columns .wp-block-comment-content p {
  margin-bottom: 0.4em;
}

/* WP Block Post Comments Form */
.wp-block-post-comments-form .comment-form-cookies-consent {
  display: inline-block;
}

/* 10. Footer areas
-------------------------------------------------------------- */
.footer_wrap {
  position: relative;
}
.footer_wrap p {
  margin: 0;
}

/* Footer widgets */
.footer_wrap .widget[class*="column-"] {
  margin: calc( var(--theme-var-grid_gap) / 2 * 1.25 ) 0;
  padding-top: 0 !important;
}

.footer_widgets_wrap {
  border-top: 1px solid var(--theme-color-bd_color);
  padding: calc(var(--theme-var-main_content_padding) - calc( var(--theme-var-grid_gap) / 2 * 1.25 )) 0;
}

/* Copyright area in the default footer */
.footer_copyright_inner {
  font-size: 0.9375em;
  padding: 2rem 0;
  overflow: hidden;
  text-align: center;
  border-top: 1px solid var(--theme-color-bd_color);
}
.footer_copyright_inner p {
  margin: 0;
}

/* 11. Utils
-------------------------------------------------------------- */
/* Customizer message */
.elementra_customizer_message {
  padding: var(--theme-var-grid_gap);
  margin: 0 auto;
  max-width: 60%;
  background-color: var(--theme-color-bg_color_2);
  border-left: 3px solid var(--theme-color-link);
}
.elementra_customizer_message b {
  color: var(--theme-color-title);
}

/* Service Messages */
form .trx_addons_message_box {
  min-width: auto;
  max-width: 90%;
}
form .error_field {
  border-color: #E93C2A !important;
}

.trx_addons_message_box {
  text-align: center;
  padding: 0.8em 1em;
  font-size: 0.9375em;
  line-height: 1.3em;
  -webkit-box-sizing: border-box;
  -ms-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-border-radius: var(--theme-var-global-border-radius-small, 0);
  -ms-border-radius: var(--theme-var-global-border-radius-small, 0);
  border-radius: var(--theme-var-global-border-radius-small, 0);
}
.trx_addons_message_box p + p {
  margin-top: 0.25em;
}
.trx_addons_message_box.trx_addons_message_box_error {
  color: #E93C2A;
  border-color: #FAE1E1;
  background-color: #FEF9F9;
  -webkit-box-shadow: 0 2px 8px #F8EBEB94;
  -ms-box-shadow: 0 2px 8px #F8EBEB94;
  box-shadow: 0 2px 8px #F8EBEB94;
}
.trx_addons_message_box.trx_addons_message_box_success {
  color: #71BA55;
  border-color: #DCEFD5;
  background-color: #F4FCF1;
  -webkit-box-shadow: 0 2px 8px #EBF8F094;
  -ms-box-shadow: 0 2px 8px #EBF8F094;
  box-shadow: 0 2px 8px #EBF8F094;
}
.trx_addons_message_box.trx_addons_message_box_info {
  color: #5DCCFF;
  border-color: #CCE6F2;
  background-color: #E5F3F9;
  -webkit-box-shadow: 0 2px 8px #EBF4F894;
  -ms-box-shadow: 0 2px 8px #EBF4F894;
  box-shadow: 0 2px 8px #EBF4F894;
}

/* 12. Third part plugins
------------------------------------------------------------------------------------ */
/* Google conversion */
iframe[name="google_conversion_frame"] {
  height: 1px;
  min-height: 0px;
  display: none;
}

/* Magnific popup */
.mfp-bg {
  z-index: 200001 !important;
  background-color: var(--theme-color-bg_color_07);
}

.mfp-wrap {
  z-index: 200002 !important;
}

.mfp-arrow {
  background-color: transparent !important;
}

.mfp-image-holder .mfp-close,
.mfp-iframe-holder .mfp-close {
  right: -20px;
  padding: 0;
  width: 44px;
  text-align: center;
  color: var(--theme-color-link);
  background: none !important;
}

.mfp-image-holder .mfp-close:hover,
.mfp-image-holder .mfp-close:focus,
.mfp-iframe-holder .mfp-close:hover,
.mfp-iframe-holder .mfp-close:focus,
.mfp-close-btn-in .mfp-close:hover,
.mfp-close-btn-in .mfp-close:focus {
  color: var(--theme-color-hover);
  background: transparent;
}

.mfp-figure img.mfp-img {
  padding-bottom: 0;
  max-height: 75vh;
}

.mfp-title, .mfp-counter {
  color: var(--theme-color-title);
}

/* MetForm */
.mf-checkbox-option input[type="checkbox"] + span:before {
  font-family: "fontello" !important;
  font-weight: 400 !important;
  padding: 0 1px;
  content: '\e804';
}

.mf-checkbox-option input[type="checkbox"]:checked + span:before {
  font-family: "fontello" !important;
  content: '\e805';
}

.mf-response-msg-wrap {
  background-color: transparent;
}

.mf-main-response-wrap:not(.mf_pro_activated) .mf-response-msg {
  background-color: var(--theme-color-bg_color);
}

.mf-main-response-wrap .mf-response-msg {
  -webkit-border-radius: var(--theme-var-global-border-radius-small, 0);
  -ms-border-radius: var(--theme-var-global-border-radius-small, 0);
  border-radius: var(--theme-var-global-border-radius-small, 0);
  border-color: var(--theme-color-bd_color);
}

.mf-success-icon, .wf-error-res .mf-alert-icon {
  font-size: 18px;
}

.mf-success-icon {
  color: var(--theme-color-title);
}

.mf-response-msg p {
  font-size: var(--theme-font-p_font-size);
  color: var(--theme-color-title);
}

/* 13. User utilities
------------------------------------------------------------------------------------ */
.nowrap {
  white-space: nowrap;
}

/*# sourceMappingURL=style.css.map */
