{"$schema": "https://schemas.wp.org/trunk/theme.json", "version": 2, "settings": {"appearanceTools": true, "spacing": {"units": ["%", "px", "em", "rem", "vh", "vw"], "blockGap": null}, "color": {"palette": [{"slug": "theme-color-bg-color", "name": "Background", "color": "#FFFFFF"}, {"slug": "theme-color-bg-color-2", "name": "Background 2", "color": "#F6F7F1"}, {"slug": "theme-color-bd-color", "name": "Border", "color": "#E5E7DE"}, {"slug": "theme-color-title", "name": "Heading", "color": "#1F242E"}, {"slug": "theme-color-meta", "name": "Text Meta", "color": "#ACAFB2"}, {"slug": "theme-color-link", "name": "Accent", "color": "#FF5E2E"}, {"slug": "theme-color-hover", "name": "Hover", "color": "#ED4C1C"}], "duotone": [{"colors": ["#FFFFFF", "#86898C"], "slug": "bg-and-text", "name": "Background and text color"}, {"colors": ["#FFFFFF", "#1F242E"], "slug": "bg-and-dark", "name": "Background and dark color"}, {"colors": ["#FFFFFF", "#FF5E2E"], "slug": "bg-and-link", "name": "Background and link color"}], "gradients": [{"slug": "vertical-link-to-hover", "gradient": "linear-gradient(to bottom,var(--theme-color-link) 0%,var(--theme-color-hover) 100%)", "name": "Vertical from link color to hover color"}, {"slug": "diagonal-link-to-hover", "gradient": "linear-gradient(to bottom right,var(--theme-color-link) 0%,var(--theme-color-hover) 100%)", "name": "Diagonal from link color to hover color"}]}, "typography": {"fluid": true, "dropCap": true, "fontFamilies": [{"fontFamily": "Inter,sans-serif", "name": "Inter,sans-serif", "slug": "p-font"}, {"fontFamily": "inherit", "name": "inherit", "slug": "post-font"}, {"fontFamily": "\"Inter Tight\",sans-serif", "name": "\"Inter Tight\",sans-serif", "slug": "h1-font"}]}, "layout": {"contentSize": "850px", "wideSize": "1290px"}, "custom": {"spacing": {"tiny": "var(--sc-space-tiny,   1rem)", "small": "var(--sc-space-small,  2rem)", "medium": "var(--sc-space-medium, 3.3333rem)", "large": "var(--sc-space-large,  6.6667rem)", "huge": "var(--sc-space-huge,   8.6667rem)"}}}, "styles": {"spacing": {"blockGap": 0}, "blocks": {"core/button": {"border": {"radius": "0"}, "color": {"background": "var(--theme-color-text_link)", "text": "var(--theme-color-inverse_link)"}, "typography": {"fontFamily": "var(--theme-font-button_font-family)", "fontWeight": "var(--theme-font-button_font-weight)", "fontSize": "var(--theme-font-button_font-size)", "lineHeight": "var(--theme-font-button_line-height)"}}, "core/post-comments": {"spacing": {"padding": {"top": "var(--wp--custom--spacing--small)"}}}, "core/pullquote": {"border": {"width": "1px 0"}}, "core/quote": {"border": {"width": "1px"}}}}}