/*
Theme Name: Elementra
Theme URI: https://elementra.themerex.net/
Description: Elementra is a Premium WordPress theme that has built-in support for popular Page Builders, slider with swipe gestures, and is SEO- and Retina-ready. The unique system of inheritance and override options allows setting up individual parameters for different sections of your site and supported plugins.
Author: ThemeREX
Author URI: https://themerex.net/
Version: 1.0.6
Tested up to: 6.8
Requires at least: 5.5
Requires PHP: 7.4
License: GNU General Public License v2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html
Tags: blog, e-commerce, portfolio, grid-layout, one-column, two-columns, three-columns, four-columns, left-sidebar, right-sidebar, custom-background, custom-colors, custom-header, custom-logo, custom-menu, editor-style, featured-image-header, featured-images, flexible-header, footer-widgets, full-width-template, microformats, post-formats, sticky-post, theme-options, threaded-comments, translation-ready, block-styles, wide-blocks
Text Domain: elementra
*/
/* TABLE OF CONTENTS:

1. Reset tags and predefined classes
2+ See in the active skin styles

-------------------------------------------------------------- */
/* 1. Reset tags
-------------------------------------------------------------- */
html, body, div, span, applet, object, iframe, video, audio,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, font, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend, input, textarea, button,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, address, section {
  font-family: inherit;
  font-size: 100%;
  line-height: inherit;
  font-weight: inherit;
  font-style: inherit;
  outline: 0;
  -ms-word-wrap: break-word;
  word-wrap: break-word;
}

applet, object, iframe, video, audio,
fieldset, form, label, legend, input, textarea, button,
table, caption, tbody, tfoot, thead, tr, th, td {
  border: 0;
}

html {
  font-size: 100%;
  /*62.5%;*/
                	          		/* Corrects text resizing oddly in IE6/7 when body font-size is set using em units
									   http://clagnut.com/blog/348/#c790 */
  -webkit-text-size-adjust: 100%;
  /* Prevents iOS text size adjust after orientation change, without disabling user zoom */
  -ms-text-size-adjust: 100%;
                                	/* www.456bereastreet.com/archive/201012/
									   controlling_text_size_in_safari_for_ios_without_disabling_user_zoom/ */
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
html:not(.edit-post-visual-editor) {
  height: 100%;
  overflow-y: scroll !important;
  overflow-x: hidden;
}

html:not(.edit-post-visual-editor),
body:not(.edit-post-visual-editor) {
  margin: 0;
  padding: 0;
  width: 100%;
}

div.ui-widget,
div.ui-widget .ui-widget,
div.ui-widget .ui-widget-header,
div.ui-widget .ui-widget-content,
div.ui-accordion .ui-accordion-header,
div.ui-state-default,
div.ui-widget-content .ui-state-default,
div.ui-widget-header .ui-state-default {
  font-family: inherit;
  font-size: 1em;
  line-height: inherit;
}

div.ui-widget,
div.ui-widget .ui-widget,
div.ui-widget .ui-widget-header,
div.ui-widget .ui-widget-content,
div.ui-accordion .ui-accordion-header,
div.ui-state-default,
div.ui-widget-content .ui-state-default,
div.ui-widget-header .ui-state-default {
  color: inherit;
  background: none;
  border-style: none;
  -webkit-border-radius: 0;
  -ms-border-radius: 0;
  border-radius: 0;
}

div.ui-datepicker {
  background-color: #fff;
}

div.ui-accordion .ui-accordion-header {
  padding: 0;
}

div.ui-accordion .ui-accordion-header-icon {
  display: none;
}

div.ui-tabs,
div.ui-tabs .ui-tabs-panel,
div.ui-tabs .ui-tabs-nav {
  padding: 0;
}

div.ui-tabs .ui-tabs-nav li {
  float: none;
  margin: 0;
}

div.ui-tabs .ui-tabs-nav .ui-tabs-anchor {
  float: none;
}

/* Predefined classes for users
-------------------------------------------------------------- */
/* Round object corners */
.rounded_none {
  -webkit-border-radius: 0;
  -ms-border-radius: 0;
  border-radius: 0;
}

.rounded_tiny {
  -webkit-border-radius: 4px;
  -ms-border-radius: 4px;
  border-radius: 4px;
}

.rounded_small {
  -webkit-border-radius: 6px;
  -ms-border-radius: 6px;
  border-radius: 6px;
}

.rounded_medium {
  -webkit-border-radius: 10px;
  -ms-border-radius: 10px;
  border-radius: 10px;
}

.rounded_large {
  -webkit-border-radius: 15px;
  -ms-border-radius: 15px;
  border-radius: 15px;
}

.rounded {
  -webkit-border-radius: 50%;
  -ms-border-radius: 50%;
  border-radius: 50%;
}

/* Overflow hidden */
.overflow_hidden {
  overflow: hidden !important;
}

.overflow_x_hidden {
  overflow-x: hidden !important;
}

.overflow_y_hidden {
  overflow-y: hidden !important;
}

/*# sourceMappingURL=style.css.map */
