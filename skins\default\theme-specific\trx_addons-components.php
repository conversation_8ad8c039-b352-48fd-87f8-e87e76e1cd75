<?php
//Allowed components
$components = array(
		'components_present' => 1,
		'components_api_elementor' => 1,
		'components_api_gutenberg' => 1,
		'components_api_js_composer' => 0,
		'components_api_vc-extensions-bundle' => 0,
		'components_api_ai-engine' => 1,
		'components_api_bbpress' => 0,
		'components_api_booked' => 0,
		'components_api_calculated-fields-form' => 0,
		'components_api_contact-form-7' => 0,
		'components_api_content_timeline' => 0,
		'components_api_easy-digital-downloads' => 0,
		'components_api_elegro-payment' => 0,
		'components_api_essential-grid' => 0,
		'components_api_give' => 0,
		'components_api_instagram-feed' => 0,
		'components_api_learnpress' => 0,
		'components_api_mailchimp-for-wp' => 0,
		'components_api_mp-timetable' => 0,
		'components_api_powerkit' => 0,
		'components_api_revslider' => 0,
		'components_api_the-events-calendar' => 0,
		'components_api_the-events-calendar_layouts_sc' => array(
				'default' => 0,
				'classic' => 0
				),
		'components_api_tourmaster' => 0,
		'components_api_trx_donations' => 0,
		'components_api_trx_popup' => 0,
		'components_api_twitter' => 0,
		'components_api_ubermenu' => 0,
		'components_api_woocommerce' => 0,
		'components_api_sitepress-multilingual-cms' => 1,
		'components_api_wp-gdpr-compliance' => 0,
		'components_api_gdpr-framework' => 0,
		'components_cpt_cars' => 0,
		'components_cpt_cars_layouts_arh' => array(
				'default_1' => 1,
				'default_2' => 1,
				'default_3' => 1
				),
		'components_cpt_cars_layouts_sc' => array(
				'default' => 1,
				'slider' => 1
				),
		'components_cpt_certificates' => 0,
		'components_cpt_courses' => 0,
		'components_cpt_courses_layouts_arh' => array(
				'default_2' => 1,
				'default_3' => 1
				),
		'components_cpt_dishes' => 0,
		'components_cpt_dishes_layouts_arh' => array(
				'default_2' => 1,
				'default_3' => 1
				),
		'components_cpt_dishes_layouts_sc' => array(
				'default' => 1,
				'float' => 1,
				'compact' => 1
				),
		'components_cpt_layouts' => 1,
		'components_cpt_layouts_layouts_sc' => array(
				'blog_item' => 0,
				'cart' => 1,
				'container' => 0,
				'currency' => 0,
				'featured' => 0,
				'iconed_text' => 0,
				'layouts' => 1,
				'language' => 1,
				'login' => 1,
				'logo' => 1,
				'menu' => 1,
				'meta' => 0,
				'search' => 1,
				'title' => 0,
				'widgets' => 0
				),
		'components_cpt_portfolio' => 0,
		'components_cpt_portfolio_layouts_arh' => array(
				'default_2' => 1,
				'default_3' => 1
				),
		'components_cpt_post' => 0,
		'components_cpt_properties' => 0,
		'components_cpt_properties_layouts_arh' => array(
				'default_1' => 1,
				'default_2' => 1,
				'default_3' => 1
				),
		'components_cpt_properties_layouts_sc' => array(
				'default' => 1,
				'slider' => 1,
				'map' => 1
				),
		'components_cpt_resume' => 0,
		'components_cpt_services' => 0,
		'components_cpt_services_layouts_arh' => array(
				'default_2' => 1,
				'default_3' => 1,
				'light_2' => 1,
				'light_3' => 1,
				'callouts_2' => 1,
				'callouts_3' => 1,
				'chess_1' => 1,
				'chess_2' => 1,
				'hover_2' => 1,
				'hover_3' => 1,
				'iconed_2' => 1,
				'iconed_3' => 1
				),
		'components_cpt_services_layouts_sc' => array(
				'default' => 1,
				'light' => 1,
				'iconed' => 1,
				'callouts' => 1,
				'list' => 1,
				'hover' => 1,
				'chess' => 1,
				'timeline' => 1,
				'tabs' => 1,
				'tabs_simple' => 1,
				'panel' => 1
				),
		'components_cpt_sport' => 0,
		'components_cpt_sport_layouts_arh' => array(
				'default_2' => 1,
				'default_3' => 1
				),
		'components_cpt_team' => 0,
		'components_cpt_team_layouts_arh' => array(
				'default_2' => 1,
				'default_3' => 1
				),
		'components_cpt_team_layouts_sc' => array(
				'default' => 1,
				'short' => 1,
				'featured' => 1
				),
		'components_cpt_testimonials' => 0,
		'components_cpt_testimonials_layouts_sc' => array(
				'default' => 1,
				'simple' => 1
				),
		'components_sc_action' => 0,
		'components_sc_action_layouts_sc' => array(
				'default' => 1,
				'simple' => 1,
				'event' => 1
				),
		'components_sc_anchor' => 0,
		'components_sc_accordionposts' => 0,
		'components_sc_blogger' => 0,
		'components_sc_blogger_layouts_sc' => array(
				'default' => 1,
				'wide' => 1,
				'list' => 1,
				'news' => 1,
				'panel' => 1,
				'cards' => 1
				),
		'components_sc_button' => 1,
		'components_sc_button_layouts_sc' => array(
				'default' => 1,
				'bordered' => 0,
				'simple' => 0
				),
		'components_sc_countdown' => 0,
		'components_sc_countdown_layouts_sc' => array(
				'default' => 1,
				'circle' => 1
				),
		'components_sc_cover' => 1,
		'components_sc_form' => 0,
		'components_sc_form_layouts_sc' => array(
				'default' => 1,
				'modern' => 1,
				'detailed' => 1
				),
		'components_sc_googlemap' => 1,
		'components_sc_googlemap_layouts_sc' => array(
				'default' => 1,
				'detailed' => 0
				),
		'components_sc_hotspot' => 1,
		'components_sc_hscroll' => 0,
		'components_sc_icompare' => 1,
		'components_sc_icons' => 0,
		'components_sc_icons_layouts_sc' => array(
				'default' => 1,
				'modern' => 1
				),
		'components_sc_osmap' => 1,
		'components_sc_osmap_layouts_sc' => array(
				'default' => 1,
				'detailed' => 0
				),
		'components_sc_price' => 0,
		'components_sc_promo' => 0,
		'components_sc_promo_layouts_sc' => array(
				'default' => 1,
				'modern' => 1,
				'blockquote' => 1
				),
		'components_sc_skills' => 0,
		'components_sc_skills_layouts_sc' => array(
				'pie' => 1,
				'counter' => 1
				),
		'components_sc_socials' => 0,
		'components_sc_socials_layouts_sc' => array(
				'default' => 1,
				'names' => 1,
				'icons_names' => 1
				),
		'components_sc_squeeze' => 0,
		'components_sc_supertitle' => 0,
		'components_sc_switcher' => 1,
		'components_sc_switcher_layouts_sc' => array(
				'default' => 1,
				'modern' => 1,
				'tabs' => 1
				),
		'components_sc_table' => 0,
		'components_sc_title' => 0,
		'components_sc_title_layouts_sc' => array(
				'default' => 1,
				'shadow' => 1,
				'accent' => 1,
				'gradient' => 1
				),
		'components_sc_users' => 0,
		'components_sc_users_layouts_sc' => array(
				'default' => 1,
				'list' => 1
				),
		'components_widgets_aboutme' => 0,
		'components_widgets_audio' => 1,
		'components_widgets_banner' => 0,
		'components_widgets_calendar' => 0,
		'components_widgets_categories_list' => 0,
		'components_widgets_categories_list_layouts_sc' => array(
				'1' => 1,
				'2' => 1,
				'3' => 1,
				'4' => 1
				),
		'components_widgets_contacts' => 0,
		'components_widgets_custom_links' => 0,
		'components_widgets_flickr' => 0,
		'components_widgets_instagram' => 0,
		'components_widgets_popular_posts' => 0,
		'components_widgets_recent_news' => 0,
		'components_widgets_recent_news_layouts_sc' => array(
				'news-announce' => 1,
				'news-excerpt' => 1,
				'news-magazine' => 1,
				'news-portfolio' => 1
				),
		'components_widgets_recent_posts' => 1,
		'components_widgets_slider' => 1,
		'components_widgets_slider_layouts_sc' => array(
				'default' => 1,
				'modern' => 0
				),
		'components_widgets_socials' => 0,
		'components_widgets_twitter' => 0,
		'components_widgets_twitter_layouts_sc' => array(
				'list' => 1,
				'default' => 1
				),
		'components_widgets_video' => 1,
		'components_widgets_video_list' => 0,
		'components_widgets_rating_posts' => 0,
		'components_components_dashboard_widget' => 1,
		'components_components_editor' => 0,
		'components_components_extended-taxonomy' => 0,
		'components_components_lazy-load' => 0,
		'components_components_reviews' => 0,
		'components_components_web_push' => 0,
		'components_api_trx_addons' => 1,
		'components_api_trx_updater' => 1,
		'components_api_envato-market' => 0,
		'components_api_metform' => 1
		);
?>