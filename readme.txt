== Elementra ==
Version: 1.0.4
Copyright: 2025, ThemeREX
WordPress version: required at least 5.5, tested up to 6.8
License: GNU General Public License v2.0 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html


I. Installation

1. In your admin panel, go to 'Appearance > Themes' and click the 'Add New' button.
2. Click 'Upload' and 'Choose File', then select the theme's .zip file. Click 'Install Now'.
3. Click 'Activate' to use your new theme right away.


II. Theme Features Usage

All available options can be used from 'Appearance->Customize' or from 'Theme Panel->Theme Options'.

For the full realization of their capabilities, the theme requires a satellite plugin 'ThemeREX Addons'.
Its installation is offered immediately after theme activation. You can also install it later
from the menu 'Appearance->Install plugins'.
Please install and activate it before starting to use the theme Elementra.

Also the theme supports many popular plugins such as 'Elementor' and many more.
They can be installed after theme activation from the menu 'Theme Panel->Theme Dashboard->Plugins'.


III. Documentation

Theme documentation is available on https://doc.themerex.net/elementra/


IV. Resources

1) Google fonts
   Licensed under one of the following: (SIL Open Font License, 1.1), (Apache License, version 2.0), or (Ubuntu Font License, 1.0)

2) Icons (css/font-icons/*.*)
   The icon set used in Elementra is Fontello.
   Copyright: Roman Shmelev (shmelev), Vitaly Puzrin (puzrin), Aleksey Zapparov (ixti), Evgeny Shkuropat (shkuropat), Vladimir Zapparov (dervus)
   Resource URI: http://fontello.com
   License: MIT license (http://opensource.org/licenses/MIT)
   License URI: https://github.com/fontello/fontello/blob/master/LICENSE

3) js/bideo.js
   Copyright: Rishabh (https://github.com/rishabhp)
   Resource URI: https://github.com/rishabhp/bideo.js?utm_source=hashnode.com
   License: MIT license (http://opensource.org/licenses/MIT)

4) js/colorpicker/spectrum/spectrum.js
   Copyright: Brian Grinstead (https://github.com/bgrins)
   Resource URI: https://github.com/bgrins/spectrum
   License: MIT license (http://opensource.org/licenses/MIT)

5) js/jquery.tubular.js
   Copyright: Sean McCambridge
   Resource URI: http://www.seanmccambridge.com/tubular
   License: MIT license (http://opensource.org/licenses/MIT)
   License URI: https://code.google.com/archive/p/jquery-tubular/

6) js/superfish/superfish.js
   Copyright: Joel Birch
   Resource URI: https://github.com/joeldbirch/superfish/
   License: MIT license (http://opensource.org/licenses/MIT)
   License URI: js/superfish/MIT-LICENSE.txt

7) Images come from:
   https://depositphotos.com/
   https://peopleimages.com/
   https://unsplash.com/
   https://flickr.com/
   https://www.shutterstock.com/
   https://www.pexels.com/
   https://www.freepik.com/
   https://www.midjourney.com/
   https://elements.envato.com/
   https://www.lummi.ai/
   License: watermark distribution only

Other custom js files and images are our own creation and is licensed under the same license as this theme.