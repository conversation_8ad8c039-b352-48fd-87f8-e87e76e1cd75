@import "../../css/_mixins.scss";
@import "../../css/_theme-vars.scss";

/* About Theme
----------------------------------------------------------------*/
.elementra_about {
	margin: 2em 2em 0 0;
	padding: 6em 3em;
	background-color: #fff;
	border: 1px solid #ddd;
	@include border-radius(2px);
	@include flex;
	@include flex-direction(column);
	@include flex-align-content(center);
	text-align: center;
}
@media (max-width: 959px) {
	.elementra_about {
		margin: 1em 1em 0 0;
		padding: 2em;
	}
}

.elementra_about_logo {
	margin: 0 0 2em 0;
}
.elementra_about_logo img {
	max-width: 30%;
	height: auto;
}

.elementra_about_title {
	margin-top:0;
	line-height:1.5em;
}

.elementra_about_description {
	margin-bottom: 3em;
	line-height:1.5em;
}
.elementra_about_description p {
	@include font(1.25em, 1.7em);
	margin: 0.5em 0 0;
}
.elementra_about_description b,
.elementra_about_description strong {
	color: #333;
}

.elementra_about_buttons .elementra_plugins_installer_link.button {
	height: 40px;
	line-height: 38px;
	padding-left: 16px;
	padding-right: 16px;
	font-size: 15px;
}
.elementra_about_buttons .elementra_plugins_installer_link.button.updating-message:before {
	line-height: 38px;
	margin-top: 1px;
}
.elementra_about_buttons .elementra_plugins_installer_link.process_now {
	cursor: not-allowed;
}

.elementra_about_notes {
	margin-top: 5em;
	color: #ccc;
}
.elementra_about_notes p {
	@include font(1.25em, 1.7em);
	margin: 0.5em 20% 0;
}
