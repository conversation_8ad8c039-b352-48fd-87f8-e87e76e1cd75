@import "../../../../css/_mixins.scss";
@import "../../../../css/_theme-vars.scss";
@import "../../css/_skin-vars.scss";

//@mixin theme--md() {
@media #{$media_md} {
	/* Single post header */
	.post_header_wrap_style_style-2 .post_featured.post_featured_bg {
		height: 30rem;
	}
}

//@mixin theme--sm() {
@media #{$media_sm} {
	/* Single post header */
	.single_style_style-2 .page_content_wrap > .content_wrap {
		padding-top: 3em;
	} 
	.post_header_wrap_style_style-2 {
		margin-top: 3em;
		.post_featured.post_featured_bg	{
			height: 20rem;
		}
		.post_featured + .post_header {
			margin-bottom: 2em;
		}
	}
}

//@mixin theme--xs() {
@media #{$media_xs} {
    /* Single post header */
	.single_style_style-2 .page_content_wrap > .content_wrap {
		padding-top: 2em;
	} 
	.post_header_wrap_style_style-2 {
		margin-top: 2em;
	}
}