<?php
/* ThemeREX Updater support functions
------------------------------------------------------------------------------- */


// Theme init priorities:
// 9 - register other filters (for installer, etc.)
if ( ! function_exists( 'elementra_trx_updater_theme_setup9' ) ) {
	add_action( 'after_setup_theme', 'elementra_trx_updater_theme_setup9', 9 );
	function elementra_trx_updater_theme_setup9() {
		if ( is_admin() ) {
			add_filter( 'elementra_filter_tgmpa_required_plugins', 'elementra_trx_updater_tgmpa_required_plugins', 8 );
			add_filter( 'trx_updater_filter_original_theme_slug', 'elementra_trx_updater_original_theme_slug' );
		}
	}
}

// Filter to add in the required plugins list
// Priority 8 is used to add this plugin before all other plugins
if ( ! function_exists( 'elementra_trx_updater_tgmpa_required_plugins' ) ) {
	//Handler of the add_filter( 'elementra_filter_tgmpa_required_plugins',	'elementra_trx_updater_tgmpa_required_plugins', 8 );
	function elementra_trx_updater_tgmpa_required_plugins( $list = array() ) {
		// License check removed - skip TRX Updater installation to prevent download errors
		// Theme updates are not needed since license checks have been removed
		return $list;
	}
}

// Check if plugin installed and activated
if ( ! function_exists( 'elementra_exists_trx_updater' ) ) {
	function elementra_exists_trx_updater() {
		return defined( 'TRX_UPDATER_VERSION' );
	}
}

// Return original theme slug
if ( ! function_exists( 'elementra_trx_updater_original_theme_slug' ) ) {
	//Handler of the add_filter( 'trx_updater_filter_original_theme_slug', 'elementra_trx_updater_original_theme_slug' );
	function elementra_trx_updater_original_theme_slug( $theme_slug ) {
		return apply_filters( 'elementra_filter_original_theme_slug', $theme_slug );
	}
}
