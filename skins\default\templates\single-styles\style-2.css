.single_style_style-2 .page_content_wrap {
  padding-top: 0;
}
.single_style_style-2 .page_content_wrap > .content_wrap {
  padding-top: calc( var(--theme-var-main_content_padding) / 2 );
}
.single_style_style-2 .post_header_single .content_wrap {
  width: var(--theme-var-content);
}
.single_style_style-2.single-format-gallery .post_header_wrap .post_featured.with_thumb .slider_outer {
  position: absolute;
  z-index: 1000;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100% !important;
}
.single_style_style-2.single-format-gallery .post_header_wrap .post_featured.with_thumb .slider_outer .slider_container {
  height: 100% !important;
}

.post_header_wrap_style_style-2 {
  position: relative;
  margin-top: calc( var(--theme-var-main_content_padding) / 2 );
  text-align: center;
  overflow: hidden;
}
.header_position_over .post_header_wrap_style_style-2 {
  margin-top: 0;
}
.post_header_wrap_style_style-2.with_featured_image {
  margin-top: 0;
}
.post_header_wrap_style_style-2.with_featured_image:before {
  content: ' ';
  display: block;
  position: absolute;
  z-index: 3;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  background: rgba(0, 0, 0, 0.45);
}
.post_header_wrap_style_style-2.with_featured_image .post_header .post_title,
.post_header_wrap_style_style-2.with_featured_image .post_header .post_meta_item,
.post_header_wrap_style_style-2.with_featured_image .post_header .post_meta_item:after {
  color: var(--theme-color-alt_title);
}
.post_header_wrap_style_style-2.with_featured_image .post_header a.post_meta_item:hover {
  color: var(--theme-color-alt_title_08);
}
.post_header_wrap_style_style-2 .post_featured {
  margin-top: 0;
  margin-bottom: 0;
  z-index: 1;
  -webkit-border-radius: 0;
  -ms-border-radius: 0;
  border-radius: 0;
}
.post_header_wrap_style_style-2 .post_featured.post_featured_bg {
  height: 35rem;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}
.post_header_wrap_style_style-2 .post_featured.post_featured_bg:before {
  display: none;
}
.post_header_wrap_style_style-2 .post_featured img {
  max-width: none;
  width: 100%;
}
.post_header_wrap_style_style-2 .post_featured.with_thumb + .post_header, .post_header_wrap_style_style-2 .post_featured.with_gallery + .post_header {
  display: inline-block;
  position: absolute;
  z-index: 10;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  box-sizing: border-box;
  margin-bottom: 0;
  margin-top: 0;
}
.post_header_wrap_style_style-2 .post_featured.with_thumb + .post_header {
  pointer-events: none;
}
.post_header_wrap_style_style-2 .post_featured.with_thumb + .post_header a {
  pointer-events: initial;
}
.post_header_wrap_style_style-2 .post_header .post_meta_item.post_categories {
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
}

/*# sourceMappingURL=style-2.css.map */
