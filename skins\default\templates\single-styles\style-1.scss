@import "../../../../css/_mixins.scss";
@import "../../../../css/_theme-vars.scss";
@import "../../css/_skin-vars.scss";

.single_style_style-1 {
	.format-gallery .post_header_wrap .post_featured.with_thumb {
		> img {
			@include scale(0.998, 0.998);
		}
		.slider_outer {
			@include abs-cc(1000);
			width: 100%;
			height: 100% !important;
			.slider_container {
				height: 100% !important;
			}
		}
	}
} 

.post_header_wrap_style_style-1 {
	position: relative;
	margin-bottom: 3em;
	.post_featured {
		margin: 0;
		&.post_featured_bg {
			height: 35rem;
			&:before {
				display: none;
			}
		}
		img {
			max-width: 100%;
			width: auto;
			height: auto;
		}
	}
	.post_header {
		margin-bottom: 3em;
	} 
}