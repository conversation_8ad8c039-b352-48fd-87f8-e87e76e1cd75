/* About Theme */
.elementra_about {
	margin: 2em 0 0 2em;
}

/* Theme Options */
.elementra_options_item[class*="column-"] {
	padding: 1.25em 0 1.25em 2em;
}
.elementra_list_choice_item {
	margin-left: 20px;
	margin-right: 0;
}
.elementra_options_header {
	margin: 1em 0 0 20px;
}
.elementra_options_title {
	float: right;
}
.elementra_options_item_info {
	border-right: 0 !important;
}
.elementra_options_item_title {
	padding-left: 30px;
	padding-right: 0;
}
.elementra_options_item_info .elementra_options_item_title,
[class*="elementra_column-"] .elementra_options_item_title,
[class*="elementra_column-"] .elementra_options_item_data {
	text-align: right;
}
.elementra_range_slider_value {
	margin-left: 10px !important;
	margin-right: 0 !important;
}
.elementra_options_buttons {
	float: left;
	text-align: left;
}
.elementra_options .elementra_options_button:before {
	margin: -1px 0 0 0.3em;
}
div.elementra_tabs_vertical > ul.ui-tabs-nav > li.ui-state-active {
	border-right: 3px solid #11a0d2;
	border-left: 0;
}
div.elementra_tabs_vertical > ul.ui-tabs-nav > li.ui-state-active > a.ui-tabs-anchor {
	right: 1px;
	left: auto;
	padding-right: 10px;
	padding-left: 13px;
}
div.elementra_tabs > ul.ui-tabs-nav > li > a > i[class*="dashicons-"],
div.elementra_tabs > ul.ui-tabs-nav > li > a > i[class*="icon-"] {
	margin: 0 0 0 0.5em;
}
div.elementra_tabs_vertical > ul.ui-tabs-nav > li.elementra_tabs_title_super > a.ui-tabs-anchor {
	padding-left: 2em;
	padding-right: 13px;
}
div.elementra_tabs_vertical > ul.ui-tabs-nav > li.elementra_tabs_title_super:after {
	content: '\e858';
	left: 12px;
	right: auto;
}
div.elementra_tabs_vertical > ul.ui-tabs-nav > li.elementra_tabs_title_sub > a.ui-tabs-anchor,
div.elementra_tabs_vertical > ul.ui-tabs-nav > li.elementra_tabs_title_sub.ui-state-active > a.ui-tabs-anchor {
	padding: 0.6em 2.95em 0.6em 0.6em;
}
.elementra_range_slider_units {
	margin-right: -8px;
	margin-left: 0;
}
.elementra_options_item_radio .elementra_options_item_holder,
.elementra_options input[type="radio"] + .elementra_options_item_holder {
	margin-left: 0.5em;
	margin-right: 0;
}
.elementra_options_item_radio .elementra_options_item_label {
	margin: 0 0 0 1em;
}
.elementra_checklist.elementra_sortable .elementra_checklist_item_label {
	padding: 6px 6px 6px 2.5em;
}
.elementra_options_item_checkbox input[type="checkbox"],
.elementra_options_item_checklist input[type="checkbox"] {
	margin-left: 0.5em;
	margin-right: 0;
}
.elementra_options_item_checkbox input[type="checkbox"]:checked:before,
.elementra_options_item_checklist input[type="checkbox"]:checked:before {
	right: 50%;
	left: auto;
	-webkit-transform: translate(50%, -50%);
	-ms-transform: translate(50%, -50%);
	transform: translate(50%, -50%);
}
.elementra_checklist.elementra_sortable .elementra_checklist_item_label:after {
	left: 12px;
	right: auto;
}
.elementra_scheme_editor_scheme {
	padding-left: 105px;
	padding-right: 0;
}
.elementra_scheme_editor_controls {
	left: 0;
	right: auto;
}
.elementra_scheme_editor_control {
	margin-right: 2px;
	margin-left: 0;
}
.elementra_options_inherit_off .elementra_options_inherit_lock {
	left: 4px;
	right: auto;
}
.elementra_options_inherit_lock {
	left: 2px;
	right: auto;
}
.elementra_options_item_image .elementra_media_selector,
.elementra_options_item_media .elementra_media_selector,
.elementra_options_item_audio .elementra_media_selector,
.elementra_options_item_video .elementra_media_selector {
	float: none !important;
}

@media (min-width: 601px) {
	.elementra_options_header.sticky {
		margin: 0 -20px 0 0;
	}
}