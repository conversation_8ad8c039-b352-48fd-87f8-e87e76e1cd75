/* ATTENTION! This file was generated automatically! Don&#039;t change it!!!
----------------------------------------------------------------------- */

/* Color scheme helpers for Customizer */
body.customize_preview {
	position: relative;
}
body.customize_preview .elementra_customizer_scheme_helper {
	display: block;
	position: absolute;
	z-index: 1;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	-webkit-box-sizing: border-box;
	    -ms-box-sizing: border-box;
	        box-sizing: border-box;
	-webkit-transition: border-color 0.3s ease;
	    -ms-transition: border-color 0.3s ease;
	        transition: border-color 0.3s ease;
	border: 2px solid #aa0000;
	pointer-events: none;
}
body.customize_preview .elementra_customizer_scheme_helper .elementra_customizer_scheme_helper_name {
	position: absolute;
	z-index: 1;
	top: 0;
	right: 0;
	color: #fff;
	background-color: #aa0000;
	display: inline-block;
	vertical-align: top;
	padding: 4px 1em;
	font-size: 11px;
	line-height: 15px;
	-webkit-transition: background-color 0.3s ease;
	    -ms-transition: background-color 0.3s ease;
	        transition: background-color 0.3s ease;
}
body.customize_preview > .elementra_customizer_scheme_helper {
	z-index: 10000;
	border-color: #2271b1;
}
body.customize_preview > .elementra_customizer_scheme_helper .elementra_customizer_scheme_helper_name {
	right: auto;
	left: 0;
	background-color: #2271b1;
}
body.customize_preview *:hover > .elementra_customizer_scheme_helper {
	border: 4px solid #00aa00;
}
body.customize_preview *:hover > .elementra_customizer_scheme_helper .elementra_customizer_scheme_helper_name {
	background-color: #00aa00;
	top: -2px;
	right: -2px;
}
