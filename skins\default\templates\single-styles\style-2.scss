@import "../../../../css/_mixins.scss";
@import "../../../../css/_theme-vars.scss";
@import "../../css/_skin-vars.scss";

.single_style_style-2 {
	.page_content_wrap {
		padding-top: 0;
		> .content_wrap {
			padding-top: calc( var(--theme-var-main_content_padding) / 2 );
		}
	}
	.post_header_single	.content_wrap 	{
		width: var(--theme-var-content);
	}
	&.single-format-gallery {
		.post_header_wrap .post_featured.with_thumb {
			.slider_outer {
				@include abs-cc(1000);
				width: 100%;
				height: 100% !important;
				.slider_container {
					height: 100% !important;
				}
			}
		} 
	}
}
.post_header_wrap_style_style-2 {
	position: relative;
	margin-top: calc( var(--theme-var-main_content_padding) / 2 );
	text-align: center;
	overflow: hidden;
	.header_position_over & {
		margin-top: 0;
	}
	&.with_featured_image {
		margin-top: 0;
		&:before {
			content: ' ';
			display: block;
			@include abs-cover(3);
			pointer-events: none;
			background: rgba(0,0,0, .45);
		}
		.post_header {
			.post_title,
			.post_meta_item,
			.post_meta_item:after {
				color: var(--theme-color-alt_title);
			}
			a.post_meta_item:hover {
				color: var(--theme-color-alt_title_08);
			}
		}
	} 
	.post_featured {
		margin-top: 0;
		margin-bottom: 0;
		z-index: 1;
		@include border-radius(0);
		&.post_featured_bg {
			height: 35rem;
			@include bg-cover;
			&:before {
				display: none;
			}
		}
		img {
			max-width: none;
			width: 100%;
		}
		&.with_thumb + .post_header,
		&.with_gallery + .post_header {
			display: inline-block;
			@include abs-cc(10);
			box-sizing: border-box;
			margin-bottom: 0;
			margin-top: 0;
		}
		&.with_thumb + .post_header {
			pointer-events: none;
			a {
				pointer-events: initial;
			}
		}
	}
	.post_header .post_meta_item.post_categories {
		@include flex-justify-content(center);
	}
}