@media (max-width: 1679px) {
  /* Theme vars */
  :root {
    --theme-var-size-koef: calc( 1440 / 1680 );
    --theme-var-page: min( 1290px, var(--theme-var-page_width) );
    --theme-var-sidebar: clamp( var(--theme-var-sidebar_width_min, 150px), calc( var(--theme-var-page) * var(--theme-var-sidebar_prc) * var(--theme-var-sidebar_proportional) + var(--theme-var-sidebar_width) * ( 1 - var(--theme-var-sidebar_proportional) ) ), var(--theme-var-sidebar_width_max, 500px) );
    --theme-var-sidebar_gap: clamp( var(--theme-var-sidebar_gap_width_min, 0px), calc( var(--theme-var-page) * var(--theme-var-sidebar_gap_prc) * var(--theme-var-sidebar_proportional) + var(--theme-var-sidebar_gap_width) * ( 1 - var(--theme-var-sidebar_proportional) ) ), var(--theme-var-sidebar_gap_width_max, 100px) );
    --theme-var-sidebar_and_gap: calc( var(--theme-var-sidebar) + var(--theme-var-sidebar_gap) );
    --theme-var-content: calc( var(--theme-var-page) - var(--theme-var-sidebar) - var(--theme-var-sidebar_gap) );
    --theme-var-content_narrow: calc( var(--theme-var-content) * var(--theme-var-koef_narrow) );
    --theme-var-padding_narrow: calc( var(--theme-var-content) * ( 1 - var(--theme-var-koef_narrow) ) );
  }

  .body_style_boxed {
    --theme-var-page_boxed_extra_new: calc( var(--theme-var-page_boxed_extra) * var(--theme-var-size-koef) );
    --theme-var-page_boxed: calc( var(--theme-var-page_width) + var(--theme-var-page_boxed_extra_new) * 2 );
    --theme-var-page: calc( var(--theme-var-page_boxed) - var(--theme-var-page_boxed_extra_new) * 2 );
    --theme-var-sidebar: clamp( var(--theme-var-sidebar_width_min, 150px), calc( var(--theme-var-page) * var(--theme-var-sidebar_prc) * var(--theme-var-sidebar_proportional) + var(--theme-var-sidebar_width) * ( 1 - var(--theme-var-sidebar_proportional) ) ), var(--theme-var-sidebar_width_max, 500px) );
    --theme-var-sidebar_gap: clamp( var(--theme-var-sidebar_gap_width_min, 0px), calc( var(--theme-var-page) * var(--theme-var-sidebar_gap_prc) * var(--theme-var-sidebar_proportional) + var(--theme-var-sidebar_gap_width) * ( 1 - var(--theme-var-sidebar_proportional) ) ), var(--theme-var-sidebar_gap_width_max, 100px) );
    --theme-var-sidebar_and_gap: calc( var(--theme-var-sidebar) + var(--theme-var-sidebar_gap) );
    --theme-var-content: calc( var(--theme-var-page) - var(--theme-var-sidebar) - var(--theme-var-sidebar_gap) );
    --theme-var-content_narrow: calc( var(--theme-var-content) * var(--theme-var-koef_narrow) );
    --theme-var-padding_narrow: calc( var(--theme-var-content) * ( 1 - var(--theme-var-koef_narrow) ) );
  }
}
@media (max-width: 1439px) {
  /* Theme vars */
  :root {
    --theme-var-size-koef: calc( 1280 / 1680 );
    --theme-var-page_width: 1100px;
    --theme-var-page: var(--theme-var-page_width);
    --theme-var-sidebar: clamp( var(--theme-var-sidebar_width_min, 150px), calc( var(--theme-var-page) * var(--theme-var-sidebar_prc) * var(--theme-var-sidebar_proportional) + var(--theme-var-sidebar_width) * ( 1 - var(--theme-var-sidebar_proportional) ) ), var(--theme-var-sidebar_width_max, 500px) );
    --theme-var-sidebar_gap: clamp( var(--theme-var-sidebar_gap_width_min, 0px), calc( var(--theme-var-page) * var(--theme-var-sidebar_gap_prc) * var(--theme-var-sidebar_proportional) + var(--theme-var-sidebar_gap_width) * ( 1 - var(--theme-var-sidebar_proportional) ) ), var(--theme-var-sidebar_gap_width_max, 100px) );
    --theme-var-sidebar_and_gap: calc( var(--theme-var-sidebar) + var(--theme-var-sidebar_gap) );
    --theme-var-content: calc( var(--theme-var-page) - var(--theme-var-sidebar) - var(--theme-var-sidebar_gap) );
    --theme-var-content_narrow: calc( var(--theme-var-content) * var(--theme-var-koef_narrow) );
    --theme-var-padding_narrow: calc( var(--theme-var-content) * ( 1 - var(--theme-var-koef_narrow) ) );
    --theme-var-main_content_padding: 8rem;
  }

  .body_style_boxed {
    --theme-var-page_boxed_extra_new: calc( var(--theme-var-page_boxed_extra) * var(--theme-var-size-koef) );
    --theme-var-page_boxed: var(--theme-var-page_width);
    --theme-var-page: calc( var(--theme-var-page_boxed) - var(--theme-var-page_boxed_extra_new) * 2 );
    --theme-var-sidebar: clamp( var(--theme-var-sidebar_width_min, 150px), calc( var(--theme-var-page) * var(--theme-var-sidebar_prc) * var(--theme-var-sidebar_proportional) + var(--theme-var-sidebar_width) * ( 1 - var(--theme-var-sidebar_proportional) ) ), var(--theme-var-sidebar_width_max, 500px) );
    --theme-var-sidebar_gap: clamp( var(--theme-var-sidebar_gap_width_min, 0px), calc( var(--theme-var-page) * var(--theme-var-sidebar_gap_prc) * var(--theme-var-sidebar_proportional) + var(--theme-var-sidebar_gap_width) * ( 1 - var(--theme-var-sidebar_proportional) ) ), var(--theme-var-sidebar_gap_width_max, 100px) );
    --theme-var-sidebar_and_gap: calc( var(--theme-var-sidebar) + var(--theme-var-sidebar_gap) );
    --theme-var-content: calc( var(--theme-var-page) - var(--theme-var-sidebar) - var(--theme-var-sidebar_gap) );
    --theme-var-content_narrow: calc( var(--theme-var-content) * var(--theme-var-koef_narrow) );
    --theme-var-padding_narrow: calc( var(--theme-var-content) * ( 1 - var(--theme-var-koef_narrow) ) );
  }

  .body_style_fullwide {
    --theme-var-page_fullwide_extra: 40px;
    --theme-var-page: calc( 100vw - var(--theme-var-page_fullwide_extra) * 2 );
    --theme-var-sidebar: clamp( var(--theme-var-sidebar_width_min, 150px), calc( var(--theme-var-page) * var(--theme-var-sidebar_prc) * var(--theme-var-sidebar_proportional) + var(--theme-var-sidebar_width) * ( 1 - var(--theme-var-sidebar_proportional) ) ), var(--theme-var-sidebar_width_max, 500px) );
    --theme-var-sidebar_gap: clamp( var(--theme-var-sidebar_gap_width_min, 0px), calc( var(--theme-var-page) * var(--theme-var-sidebar_gap_prc) * var(--theme-var-sidebar_proportional) + var(--theme-var-sidebar_gap_width) * ( 1 - var(--theme-var-sidebar_proportional) ) ), var(--theme-var-sidebar_gap_width_max, 100px) );
    --theme-var-sidebar_and_gap: calc( var(--theme-var-sidebar) + var(--theme-var-sidebar_gap) );
    --theme-var-content: calc( var(--theme-var-page) - var(--theme-var-sidebar) - var(--theme-var-sidebar_gap) );
    --theme-var-content_narrow: calc( var(--theme-var-content) * var(--theme-var-koef_narrow) );
    --theme-var-padding_narrow: calc( var(--theme-var-content) * ( 1 - var(--theme-var-koef_narrow) ) );
  }

  /* Core blocks */
  body.sidebar_hide .alignwide,
  body.sidebar_hide.expand_content .alignwide {
    left: 0;
    width: 100%;
  }
}
@media (max-width: 1279px) {
  /* Theme vars */
  :root {
    --theme-var-size-koef: calc( 1024 / 1680 );
    --theme-var-page_extra: 30px;
    --theme-var-page: calc( 100vw - var(--theme-var-page_extra) * 2 );
    --theme-var-sidebar_width: 290px;
    --theme-var-sidebar_gap_width: 30px;
    --theme-var-sidebar: clamp( var(--theme-var-sidebar_width_min, 150px), calc( var(--theme-var-page) * var(--theme-var-sidebar_prc) * var(--theme-var-sidebar_proportional) + var(--theme-var-sidebar_width) * ( 1 - var(--theme-var-sidebar_proportional) ) ), var(--theme-var-sidebar_width_max, 500px) );
    --theme-var-sidebar_gap: clamp( var(--theme-var-sidebar_gap_width_min, 0px), calc( var(--theme-var-page) * var(--theme-var-sidebar_gap_prc) * var(--theme-var-sidebar_proportional) + var(--theme-var-sidebar_gap_width) * ( 1 - var(--theme-var-sidebar_proportional) ) ), var(--theme-var-sidebar_gap_width_max, 100px) );
    --theme-var-sidebar_and_gap: calc( var(--theme-var-sidebar) + var(--theme-var-sidebar_gap) );
    --theme-var-content: calc( var(--theme-var-page) - var(--theme-var-sidebar) - var(--theme-var-sidebar_gap) );
    --theme-var-content_narrow: calc( var(--theme-var-content) * var(--theme-var-koef_narrow) );
    --theme-var-padding_narrow: calc( var(--theme-var-content) * ( 1 - var(--theme-var-koef_narrow) ) );
    --theme-var-main_content_padding: 5.5rem;
    --theme-var-single_post_block_margin: 4.5rem;
  }

  .body_style_boxed {
    --theme-var-page_boxed_extra: var(--theme-var-page_extra);
    --theme-var-page_boxed: 100vw;
    --theme-var-page: calc( var(--theme-var-page_boxed) - var(--theme-var-page_boxed_extra) * 2 );
    --theme-var-sidebar: clamp( var(--theme-var-sidebar_width_min, 150px), calc( var(--theme-var-page) * var(--theme-var-sidebar_prc) * var(--theme-var-sidebar_proportional) + var(--theme-var-sidebar_width) * ( 1 - var(--theme-var-sidebar_proportional) ) ), var(--theme-var-sidebar_width_max, 500px) );
    --theme-var-sidebar_gap: clamp( var(--theme-var-sidebar_gap_width_min, 0px), calc( var(--theme-var-page) * var(--theme-var-sidebar_gap_prc) * var(--theme-var-sidebar_proportional) + var(--theme-var-sidebar_gap_width) * ( 1 - var(--theme-var-sidebar_proportional) ) ), var(--theme-var-sidebar_gap_width_max, 100px) );
    --theme-var-sidebar_and_gap: calc( var(--theme-var-sidebar) + var(--theme-var-sidebar_gap) );
    --theme-var-content: calc( var(--theme-var-page) - var(--theme-var-sidebar) - var(--theme-var-sidebar_gap) );
    --theme-var-content_narrow: calc( var(--theme-var-content) * var(--theme-var-koef_narrow) );
    --theme-var-padding_narrow: calc( var(--theme-var-content) * ( 1 - var(--theme-var-koef_narrow) ) );
  }

  .body_style_fullwide {
    --theme-var-page_fullwide_extra: var(--theme-var-page_extra);
    --theme-var-page: calc( 100vw - var(--theme-var-page_fullwide_extra) * 2 );
    --theme-var-sidebar: clamp( var(--theme-var-sidebar_width_min, 150px), calc( var(--theme-var-page) * var(--theme-var-sidebar_prc) * var(--theme-var-sidebar_proportional) + var(--theme-var-sidebar_width) * ( 1 - var(--theme-var-sidebar_proportional) ) ), var(--theme-var-sidebar_width_max, 500px) );
    --theme-var-sidebar_gap: clamp( var(--theme-var-sidebar_gap_width_min, 0px), calc( var(--theme-var-page) * var(--theme-var-sidebar_gap_prc) * var(--theme-var-sidebar_proportional) + var(--theme-var-sidebar_gap_width) * ( 1 - var(--theme-var-sidebar_proportional) ) ), var(--theme-var-sidebar_gap_width_max, 100px) );
    --theme-var-sidebar_and_gap: calc( var(--theme-var-sidebar) + var(--theme-var-sidebar_gap) );
    --theme-var-content: calc( var(--theme-var-page) - var(--theme-var-sidebar) - var(--theme-var-sidebar_gap) );
    --theme-var-content_narrow: calc( var(--theme-var-content) * var(--theme-var-koef_narrow) );
    --theme-var-padding_narrow: calc( var(--theme-var-content) * ( 1 - var(--theme-var-koef_narrow) ) );
  }

  /* Columns, push, pull and offset sizes */
  .column-1-tablet,
  .column-1_1-tablet,
  .trx_addons_column-1-tablet,
  .trx_addons_column-1_1-tablet {
    width: 100%;
  }

  .column-1_2-tablet,
  .trx_addons_column-1_2-tablet {
    width: 50%;
  }

  .column-1_3-tablet,
  .trx_addons_column-1_3-tablet {
    width: 33.3333333333%;
  }

  .column-1_4-tablet,
  .trx_addons_column-1_4-tablet {
    width: 25%;
  }

  .column-1_5-tablet,
  .trx_addons_column-1_5-tablet {
    width: 20%;
  }

  .column-1_6-tablet,
  .trx_addons_column-1_6-tablet {
    width: 16.6666666667%;
  }

  .column-1_7-tablet,
  .trx_addons_column-1_7-tablet {
    width: 14.2857142857%;
  }

  .column-1_8-tablet,
  .trx_addons_column-1_8-tablet {
    width: 12.5%;
  }

  .column-1_9-tablet,
  .trx_addons_column-1_9-tablet {
    width: 11.1111111111%;
  }

  .column-1_10-tablet,
  .trx_addons_column-1_10-tablet {
    width: 10%;
  }

  .column-1_11-tablet,
  .trx_addons_column-1_11-tablet {
    width: 9.0909090909%;
  }

  .column-1_12-tablet,
  .trx_addons_column-1_12-tablet {
    width: 8.3333333333%;
  }

  .column-2_2-tablet,
  .trx_addons_column-2_2-tablet {
    width: 100%;
  }

  .column-2_3-tablet,
  .trx_addons_column-2_3-tablet {
    width: 66.6666666667%;
  }

  .column-2_4-tablet,
  .trx_addons_column-2_4-tablet {
    width: 50%;
  }

  .column-2_5-tablet,
  .trx_addons_column-2_5-tablet {
    width: 40%;
  }

  .column-2_6-tablet,
  .trx_addons_column-2_6-tablet {
    width: 33.3333333333%;
  }

  .column-2_7-tablet,
  .trx_addons_column-2_7-tablet {
    width: 28.5714285714%;
  }

  .column-2_8-tablet,
  .trx_addons_column-2_8-tablet {
    width: 25%;
  }

  .column-2_9-tablet,
  .trx_addons_column-2_9-tablet {
    width: 22.2222222222%;
  }

  .column-2_10-tablet,
  .trx_addons_column-2_10-tablet {
    width: 20%;
  }

  .column-2_11-tablet,
  .trx_addons_column-2_11-tablet {
    width: 18.1818181818%;
  }

  .column-2_12-tablet,
  .trx_addons_column-2_12-tablet {
    width: 16.6666666667%;
  }

  .column-3_3-tablet,
  .trx_addons_column-3_3-tablet {
    width: 100%;
  }

  .column-3_4-tablet,
  .trx_addons_column-3_4-tablet {
    width: 75%;
  }

  .column-3_5-tablet,
  .trx_addons_column-3_5-tablet {
    width: 60%;
  }

  .column-3_6-tablet,
  .trx_addons_column-3_6-tablet {
    width: 50%;
  }

  .column-3_7-tablet,
  .trx_addons_column-3_7-tablet {
    width: 42.8571428571%;
  }

  .column-3_8-tablet,
  .trx_addons_column-3_8-tablet {
    width: 37.5%;
  }

  .column-3_9-tablet,
  .trx_addons_column-3_9-tablet {
    width: 33.3333333333%;
  }

  .column-3_10-tablet,
  .trx_addons_column-3_10-tablet {
    width: 30%;
  }

  .column-3_11-tablet,
  .trx_addons_column-3_11-tablet {
    width: 27.2727272727%;
  }

  .column-3_12-tablet,
  .trx_addons_column-3_12-tablet {
    width: 25%;
  }

  .column-4_4-tablet,
  .trx_addons_column-4_4-tablet {
    width: 100%;
  }

  .column-4_5-tablet,
  .trx_addons_column-4_5-tablet {
    width: 80%;
  }

  .column-4_6-tablet,
  .trx_addons_column-4_6-tablet {
    width: 66.6666666667%;
  }

  .column-4_7-tablet,
  .trx_addons_column-4_7-tablet {
    width: 57.1428571429%;
  }

  .column-4_8-tablet,
  .trx_addons_column-4_8-tablet {
    width: 50%;
  }

  .column-4_9-tablet,
  .trx_addons_column-4_9-tablet {
    width: 44.4444444444%;
  }

  .column-4_10-tablet,
  .trx_addons_column-4_10-tablet {
    width: 40%;
  }

  .column-4_11-tablet,
  .trx_addons_column-4_11-tablet {
    width: 36.3636363636%;
  }

  .column-4_12-tablet,
  .trx_addons_column-4_12-tablet {
    width: 33.3333333333%;
  }

  .column-5_5-tablet,
  .trx_addons_column-5_5-tablet {
    width: 100%;
  }

  .column-5_6-tablet,
  .trx_addons_column-5_6-tablet {
    width: 83.3333333333%;
  }

  .column-5_7-tablet,
  .trx_addons_column-5_7-tablet {
    width: 71.4285714286%;
  }

  .column-5_8-tablet,
  .trx_addons_column-5_8-tablet {
    width: 62.5%;
  }

  .column-5_9-tablet,
  .trx_addons_column-5_9-tablet {
    width: 55.5555555556%;
  }

  .column-5_10-tablet,
  .trx_addons_column-5_10-tablet {
    width: 50%;
  }

  .column-5_11-tablet,
  .trx_addons_column-5_11-tablet {
    width: 45.4545454545%;
  }

  .column-5_12-tablet,
  .trx_addons_column-5_12-tablet {
    width: 41.6666666667%;
  }

  .column-6_6-tablet,
  .trx_addons_column-6_6-tablet {
    width: 100%;
  }

  .column-6_7-tablet,
  .trx_addons_column-6_7-tablet {
    width: 85.7142857143%;
  }

  .column-6_8-tablet,
  .trx_addons_column-6_8-tablet {
    width: 75%;
  }

  .column-6_9-tablet,
  .trx_addons_column-6_9-tablet {
    width: 66.6666666667%;
  }

  .column-6_10-tablet,
  .trx_addons_column-6_10-tablet {
    width: 60%;
  }

  .column-6_11-tablet,
  .trx_addons_column-6_11-tablet {
    width: 54.5454545455%;
  }

  .column-6_12-tablet,
  .trx_addons_column-6_12-tablet {
    width: 50%;
  }

  .column-7_7-tablet,
  .trx_addons_column-7_7-tablet {
    width: 100%;
  }

  .column-7_8-tablet,
  .trx_addons_column-7_8-tablet {
    width: 87.5%;
  }

  .column-7_9-tablet,
  .trx_addons_column-7_9-tablet {
    width: 77.7777777778%;
  }

  .column-7_10-tablet,
  .trx_addons_column-7_10-tablet {
    width: 70%;
  }

  .column-7_11-tablet,
  .trx_addons_column-7_11-tablet {
    width: 63.6363636364%;
  }

  .column-7_12-tablet,
  .trx_addons_column-7_12-tablet {
    width: 58.3333333333%;
  }

  .column-8_8-tablet,
  .trx_addons_column-8_8-tablet {
    width: 100%;
  }

  .column-8_9-tablet,
  .trx_addons_column-8_9-tablet {
    width: 88.8888888889%;
  }

  .column-8_10-tablet,
  .trx_addons_column-8_10-tablet {
    width: 80%;
  }

  .column-8_11-tablet,
  .trx_addons_column-8_11-tablet {
    width: 72.7272727273%;
  }

  .column-8_12-tablet,
  .trx_addons_column-8_12-tablet {
    width: 66.6666666667%;
  }

  .column-9_9-tablet,
  .trx_addons_column-9_9-tablet {
    width: 100%;
  }

  .column-9_10-tablet,
  .trx_addons_column-9_10-tablet {
    width: 90%;
  }

  .column-9_11-tablet,
  .trx_addons_column-9_11-tablet {
    width: 81.8181818182%;
  }

  .column-9_12-tablet,
  .trx_addons_column-9_12-tablet {
    width: 75%;
  }

  .column-10_10-tablet,
  .trx_addons_column-10_10-tablet {
    width: 100%;
  }

  .column-10_11-tablet,
  .trx_addons_column-10_11-tablet {
    width: 90.9090909091%;
  }

  .column-10_12-tablet,
  .trx_addons_column-10_12-tablet {
    width: 83.3333333333%;
  }

  .column-11_11-tablet,
  .trx_addons_column-11_11-tablet {
    width: 100%;
  }

  .column-11_12-tablet,
  .trx_addons_column-11_12-tablet {
    width: 91.6666666667%;
  }

  .column-12_12-tablet,
  .trx_addons_column-12_12-tablet {
    width: 100%;
  }

  .row.columns_padding_bottom.columns_in_single_row > [class*="column-"][class*="-tablet"],
  .columns_wrap.columns_padding_bottom.columns_in_single_row > [class*="column-"][class*="-tablet"],
  .trx_addons_columns_wrap.columns_padding_bottom.columns_in_single_row > [class*="trx_addons_column-"][class*="-tablet"] {
    padding-bottom: var(--theme-var-grid_gap);
  }

  /* Body sizes */
  .body_style_boxed .page_wrap {
    width: 100%;
  }

  .body_style_fullscreen.fixed_blocks_sticky:not(.elementor-editor-active) .sc_stack_section_effect_slide:not(.elementor-element-edit-mode) {
    top: 0;
  }

  /* Content and Sidebar */
  body:not(.expand_content) [class*="content_wrap"] > .content {
    width: 100% !important;
    float: none !important;
  }

  [class*="content_wrap"] > .sidebar {
    float: none !important;
    margin-top: var(--theme-var-main_content_padding);
  }

  [class*="content_wrap"] > .sidebar_default {
    margin-left: calc( ( var(--theme-var-grid_gap) / 2 ) * -1 );
    margin-right: calc( ( var(--theme-var-grid_gap) / 2 ) * -1 );
    width: calc( 100% + var(--theme-var-grid_gap) ) !important;
  }

  [class*="content_wrap"] > .sidebar_custom {
    width: 100% !important;
  }

  body.body_style_fullwide.sidebar_show [class*="content_wrap"] > .content,
  body.body_style_fullscreen.sidebar_show [class*="content_wrap"] > .content {
    padding-left: 0;
    padding-right: 0;
  }

  body.body_style_fullwide.sidebar_show [class*="content_wrap"] > .sidebar_default,
  body.body_style_fullscreen.sidebar_show [class*="content_wrap"] > .sidebar_default {
    margin-left: calc( ( var(--theme-var-grid_gap) /2 ) * -1 );
    margin-right: calc( ( var(--theme-var-grid_gap) / 2 ) * -1 );
  }

  /* Sticky sidebar */
  body.fixed_blocks_sticky .sidebar {
    position: static;
    top: auto !important;
  }

  /* Core blocks */
  body.sidebar_hide .alignfull {
    margin-left: calc( -100vw / 2 + 100% / 2 );
    margin-right: calc( -100vw / 2 + 100% / 2 );
    width: 100vw;
    max-width: 100vw;
  }

  .sidebar_hide.narrow_content .alignleft.is-style-alignfar,
  .sidebar_hide.narrow_content .is-style-alignfar > .alignleft,
  .sidebar_hide.narrow_content .alignright.is-style-alignfar,
  .sidebar_hide.narrow_content .is-style-alignfar > .alignright {
    max-width: calc( var(--theme-var-content_narrow) / 2 - var(--theme-var-grid_gap) );
  }

  /* Post layouts */
  .post_item .more-link {
    margin-top: 2em;
  }

  /* Widgets */
  [class*="content_wrap"] > .sidebar_default .widget {
    display: inline-block;
    float: none;
    vertical-align: top;
    width: 50%;
    padding: 0 calc( var(--theme-var-grid_gap) / 2 );
    -webkit-box-sizing: border-box;
    -ms-box-sizing: border-box;
    box-sizing: border-box;
  }
  [class*="content_wrap"] > .sidebar_default .widget .widget {
    padding: 0;
  }
  [class*="content_wrap"] > .sidebar_default .widget + .widget {
    margin-top: 0;
  }
  [class*="content_wrap"] > .sidebar_default .widget + .widget + .widget {
    margin-top: calc( var(--theme-var-grid_gap) * 1.25 );
  }

  .widget.column-1_3, .widget.column-1_4, .widget.column-1_5, .widget.column-1_6, .widget.column-1_7, .widget.column-1_8, .widget.column-1_9, .widget.column-1_10, .widget.column-1_11, .widget.column-1_12 {
    width: 50%;
  }
}
@media (max-width: 1023px) {
  /* Theme vars */
  :root {
    --theme-var-size-koef: calc( 768 / 1680 );
    --theme-var-page_extra: 30px;
    --theme-var-page: calc( 100vw - var(--theme-var-page_extra) * 2 );
    --theme-var-sidebar: clamp( var(--theme-var-sidebar_width_min, 150px), calc( var(--theme-var-page) * var(--theme-var-sidebar_prc) * var(--theme-var-sidebar_proportional) + var(--theme-var-sidebar_width) * ( 1 - var(--theme-var-sidebar_proportional) ) ), var(--theme-var-sidebar_width_max, 500px) );
    --theme-var-sidebar_gap: clamp( var(--theme-var-sidebar_gap_width_min, 0px), calc( var(--theme-var-page) * var(--theme-var-sidebar_gap_prc) * var(--theme-var-sidebar_proportional) + var(--theme-var-sidebar_gap_width) * ( 1 - var(--theme-var-sidebar_proportional) ) ), var(--theme-var-sidebar_gap_width_max, 100px) );
    --theme-var-sidebar_and_gap: calc( var(--theme-var-sidebar) + var(--theme-var-sidebar_gap) );
    --theme-var-content: var(--theme-var-page);
    --theme-var-content_narrow: calc( var(--theme-var-content) * var(--theme-var-koef_narrow) );
    --theme-var-padding_narrow: calc( var(--theme-var-content) * ( 1 - var(--theme-var-koef_narrow) ) );
  }

  .body_style_boxed {
    --theme-var-page_boxed_extra: var(--theme-var-page_extra);
    --theme-var-page_boxed: 100vw;
    --theme-var-page: calc( var(--theme-var-page_boxed) - var(--theme-var-page_boxed_extra) * 2 );
    --theme-var-sidebar: clamp( var(--theme-var-sidebar_width_min, 150px), calc( var(--theme-var-page) * var(--theme-var-sidebar_prc) * var(--theme-var-sidebar_proportional) + var(--theme-var-sidebar_width) * ( 1 - var(--theme-var-sidebar_proportional) ) ), var(--theme-var-sidebar_width_max, 500px) );
    --theme-var-sidebar_gap: clamp( var(--theme-var-sidebar_gap_width_min, 0px), calc( var(--theme-var-page) * var(--theme-var-sidebar_gap_prc) * var(--theme-var-sidebar_proportional) + var(--theme-var-sidebar_gap_width) * ( 1 - var(--theme-var-sidebar_proportional) ) ), var(--theme-var-sidebar_gap_width_max, 100px) );
    --theme-var-sidebar_and_gap: calc( var(--theme-var-sidebar) + var(--theme-var-sidebar_gap) );
    --theme-var-content: var(--theme-var-page);
    --theme-var-content_narrow: calc( var(--theme-var-content) * var(--theme-var-koef_narrow) );
    --theme-var-padding_narrow: calc( var(--theme-var-content) * ( 1 - var(--theme-var-koef_narrow) ) );
  }

  .body_style_fullwide {
    --theme-var-page_fullwide_extra: var(--theme-var-page_extra);
    --theme-var-page: calc( 100vw - var(--theme-var-page_fullwide_extra) * 2 );
    --theme-var-sidebar: clamp( var(--theme-var-sidebar_width_min, 150px), calc( var(--theme-var-page) * var(--theme-var-sidebar_prc) * var(--theme-var-sidebar_proportional) + var(--theme-var-sidebar_width) * ( 1 - var(--theme-var-sidebar_proportional) ) ), var(--theme-var-sidebar_width_max, 500px) );
    --theme-var-sidebar_gap: clamp( var(--theme-var-sidebar_gap_width_min, 0px), calc( var(--theme-var-page) * var(--theme-var-sidebar_gap_prc) * var(--theme-var-sidebar_proportional) + var(--theme-var-sidebar_gap_width) * ( 1 - var(--theme-var-sidebar_proportional) ) ), var(--theme-var-sidebar_gap_width_max, 100px) );
    --theme-var-sidebar_and_gap: calc( var(--theme-var-sidebar) + var(--theme-var-sidebar_gap) );
    --theme-var-content: var(--theme-var-page);
    --theme-var-content_narrow: calc( var(--theme-var-content) * var(--theme-var-koef_narrow) );
    --theme-var-padding_narrow: calc( var(--theme-var-content) * ( 1 - var(--theme-var-koef_narrow) ) );
  }

  /* Leave max 5 columns in the gallery */
  .gallery.gallery-columns-9 .gallery-item {
    width: 20% !important;
  }

  .gallery.gallery-columns-8 .gallery-item {
    width: 20% !important;
  }

  .gallery.gallery-columns-7 .gallery-item {
    width: 20% !important;
  }

  .gallery.gallery-columns-6 .gallery-item {
    width: 20% !important;
  }
}
@media (max-width: 767px) {
  /* Theme vars */
  :root {
    --theme-var-size-koef: calc( 480 / 1680 );
    --theme-var-page_extra: 20px;
    --theme-var-page: calc( 100vw - var(--theme-var-page_extra) * 2 );
    --theme-var-grid_gap: 20px;
    --theme-var-grid_max_columns: 1;
    --theme-var-sidebar: clamp( var(--theme-var-sidebar_width_min, 150px), calc( var(--theme-var-page) * var(--theme-var-sidebar_prc) * var(--theme-var-sidebar_proportional) + var(--theme-var-sidebar_width) * ( 1 - var(--theme-var-sidebar_proportional) ) ), var(--theme-var-sidebar_width_max, 500px) );
    --theme-var-sidebar_gap: clamp( var(--theme-var-sidebar_gap_width_min, 0px), calc( var(--theme-var-page) * var(--theme-var-sidebar_gap_prc) * var(--theme-var-sidebar_proportional) + var(--theme-var-sidebar_gap_width) * ( 1 - var(--theme-var-sidebar_proportional) ) ), var(--theme-var-sidebar_gap_width_max, 100px) );
    --theme-var-sidebar_and_gap: calc( var(--theme-var-sidebar) + var(--theme-var-sidebar_gap) );
    --theme-var-content: var(--theme-var-page);
    --theme-var-content_narrow: var(--theme-var-content);
    --theme-var-padding_narrow: 0px;
    --theme-var-main_content_padding: 3.2rem;
    --theme-var-single_post_block_margin: 3rem;
  }

  .body_style_boxed {
    --theme-var-page_boxed_extra: var(--theme-var-page_extra);
    --theme-var-page_boxed: 100vw;
    --theme-var-page: calc( var(--theme-var-page_boxed) - var(--theme-var-page_boxed_extra) * 2 );
    --theme-var-sidebar: clamp( var(--theme-var-sidebar_width_min, 150px), calc( var(--theme-var-page) * var(--theme-var-sidebar_prc) * var(--theme-var-sidebar_proportional) + var(--theme-var-sidebar_width) * ( 1 - var(--theme-var-sidebar_proportional) ) ), var(--theme-var-sidebar_width_max, 500px) );
    --theme-var-sidebar_gap: clamp( var(--theme-var-sidebar_gap_width_min, 0px), calc( var(--theme-var-page) * var(--theme-var-sidebar_gap_prc) * var(--theme-var-sidebar_proportional) + var(--theme-var-sidebar_gap_width) * ( 1 - var(--theme-var-sidebar_proportional) ) ), var(--theme-var-sidebar_gap_width_max, 100px) );
    --theme-var-sidebar_and_gap: calc( var(--theme-var-sidebar) + var(--theme-var-sidebar_gap) );
    --theme-var-content: var(--theme-var-page);
    --theme-var-content_narrow: var(--theme-var-content);
    --theme-var-padding_narrow: 0px;
  }

  .body_style_fullwide {
    --theme-var-page_fullwide_extra: var(--theme-var-page_extra);
    --theme-var-page: calc( 100vw - var(--theme-var-page_fullwide_extra) * 2 );
    --theme-var-sidebar: clamp( var(--theme-var-sidebar_width_min, 150px), calc( var(--theme-var-page) * var(--theme-var-sidebar_prc) * var(--theme-var-sidebar_proportional) + var(--theme-var-sidebar_width) * ( 1 - var(--theme-var-sidebar_proportional) ) ), var(--theme-var-sidebar_width_max, 500px) );
    --theme-var-sidebar_gap: clamp( var(--theme-var-sidebar_gap_width_min, 0px), calc( var(--theme-var-page) * var(--theme-var-sidebar_gap_prc) * var(--theme-var-sidebar_proportional) + var(--theme-var-sidebar_gap_width) * ( 1 - var(--theme-var-sidebar_proportional) ) ), var(--theme-var-sidebar_gap_width_max, 100px) );
    --theme-var-sidebar_and_gap: calc( var(--theme-var-sidebar) + var(--theme-var-sidebar_gap) );
    --theme-var-content: var(--theme-var-page);
    --theme-var-content_narrow: var(--theme-var-content);
    --theme-var-padding_narrow: 0px;
  }

  /* Content and Sidebar */
  body:not(.expand_content):not(.body_style_fullwide):not(.body_style_fullscreen) [class*="content_wrap"] > .content {
    margin-bottom: 0;
  }

  /* Tags layouts */
  .wp-block-table td, .wp-block-table th, table td, table th {
    padding: 0.6rem;
  }

  /* Grid */
  .row.columns_padding_bottom.columns_in_single_row > [class*="column-"],
  .columns_wrap.columns_padding_bottom.columns_in_single_row > [class*="column-"],
  .trx_addons_columns_wrap.columns_padding_bottom.columns_in_single_row > [class*="trx_addons_column-"] {
    padding-bottom: var(--theme-var-grid_gap);
  }

  .row.columns_padding_bottom.columns_in_single_row > [class*="column-"]:last-child,
  .columns_wrap.columns_padding_bottom.columns_in_single_row > [class*="column-"]:last-child,
  .trx_addons_columns_wrap.columns_padding_bottom.columns_in_single_row > [class*="trx_addons_column-"]:last-child {
    padding-bottom: 0;
  }

  .row:not(.columns_padding_bottom) > [class*="column-"]:nth-child(2n+3),
  .columns_wrap:not(.columns_padding_bottom) > [class*="column-"]:nth-child(2n+3),
  .row:not(.columns_padding_bottom) > [class*="column-"]:nth-child(2n+4),
  .columns_wrap:not(.columns_padding_bottom) > [class*="column-"]:nth-child(2n+4),
  .trx_addons_columns_wrap:not(.columns_padding_bottom) > [class*="trx_addons_column-"]:nth-child(2n+3),
  .trx_addons_columns_wrap:not(.columns_padding_bottom) > [class*="trx_addons_column-"]:nth-child(2n+4) {
    padding-top: var(--theme-var-grid_gap);
  }

  /* Max column's width in the row */
  .row > [class*="column-"],
  .columns_wrap > [class*="column-"],
  .trx_addons_columns_wrap > [class*="trx_addons_column-"] {
    width: calc( 100% / var(--theme-var-grid_max_columns) );
  }

  .row > .column-1_1, .row > .column-2_2, .row > .column-3_3, .row > .column-4_4,
  .row > .column-5_5, .row > .column-6_6, .row > .column-7_7, .row > .column-8_8,
  .row > .column-9_9, .row > .column-10_10,
  .row > .column-11_11, .row > .column-12_12,
  .columns_wrap > .column-1_1, .columns_wrap > .column-2_2, .columns_wrap > .column-3_3,
  .columns_wrap > .column-4_4, .columns_wrap > .column-5_5, .columns_wrap > .column-6_6,
  .columns_wrap > .column-7_7, .columns_wrap > .column-8_8, .columns_wrap > .column-9_9,
  .columns_wrap > .column-10_10, .columns_wrap > .column-11_11, .columns_wrap > .column-12_12,
  .row > .column-2_3, .columns_wrap > .column-2_3,
  .row > .column-1_3.after_span_2, .columns_wrap > .column-1_3.after_span_2,
  .row > .column-2_4, .columns_wrap > .column-3_4,
  .row > .column-1_4.after_span_2, .columns_wrap > .column-1_4.after_span_3,
  .row > .column-2_5, .columns_wrap > .column-2_5,
  .row > .column-3_5, .columns_wrap > .column-3_5,
  .row > .column-4_5, .columns_wrap > .column-4_5,
  .row > .column-2_6, .columns_wrap > .column-2_6,
  .row > .column-3_6, .columns_wrap > .column-3_6,
  .row > .column-4_6, .columns_wrap > .column-4_6,
  .row > .column-5_6, .columns_wrap > .column-5_6,
  .trx_addons_columns_wrap > .trx_addons_column-1_1, .trx_addons_columns_wrap > .trx_addons_column-2_2,
  .trx_addons_columns_wrap > .trx_addons_column-3_3, .trx_addons_columns_wrap > .trx_addons_column-4_4,
  .trx_addons_columns_wrap > .trx_addons_column-5_5, .trx_addons_columns_wrap > .trx_addons_column-6_6,
  .trx_addons_columns_wrap > .trx_addons_column-7_7, .trx_addons_columns_wrap > .trx_addons_column-8_8,
  .trx_addons_columns_wrap > .trx_addons_column-9_9, .trx_addons_columns_wrap > .trx_addons_column-10_10,
  .trx_addons_columns_wrap > .trx_addons_column-11_11, .trx_addons_columns_wrap > .trx_addons_column-12_12,
  .trx_addons_columns_wrap > .trx_addons_column-2_3,
  .trx_addons_columns_wrap > .trx_addons_column-1_3.after_span_2,
  .trx_addons_columns_wrap > .trx_addons_column-3_4,
  .trx_addons_columns_wrap > .trx_addons_column-1_4.after_span_3,
  .trx_addons_columns_wrap > .trx_addons_column-2_5,
  .trx_addons_columns_wrap > .trx_addons_column-3_5,
  .trx_addons_columns_wrap > .trx_addons_column-4_5,
  .trx_addons_columns_wrap > .trx_addons_column-2_6,
  .trx_addons_columns_wrap > .trx_addons_column-3_6,
  .trx_addons_columns_wrap > .trx_addons_column-4_6,
  .trx_addons_columns_wrap > .trx_addons_column-5_6 {
    width: 100%;
  }

  /* Columns, push, pull and offset sizes */
  .row > .column-1-tablet, .columns_wrap > .column-1-tablet,
  .row > .column-1_1-tablet, .columns_wrap > .column-1_1-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-1-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-1_1-tablet {
    width: 100%;
  }

  .row > .column-1_2-tablet, .columns_wrap > .column-1_2-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-1_2-tablet {
    width: 50%;
  }

  .row > .column-1_3-tablet, .columns_wrap > .column-1_3-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-1_3-tablet {
    width: 33.3333333333%;
  }

  .row > .column-1_4-tablet, .columns_wrap > .column-1_4-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-1_4-tablet {
    width: 25%;
  }

  .row > .column-1_5-tablet, .columns_wrap > .column-1_5-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-1_5-tablet {
    width: 20%;
  }

  .row > .column-1_6-tablet, .columns_wrap > .column-1_6-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-1_6-tablet {
    width: 16.6666666667%;
  }

  .row > .column-1_7-tablet, .columns_wrap > .column-1_7-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-1_7-tablet {
    width: 14.2857142857%;
  }

  .row > .column-1_8-tablet, .columns_wrap > .column-1_8-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-1_8-tablet {
    width: 12.5%;
  }

  .row > .column-1_9-tablet, .columns_wrap > .column-1_9-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-1_9-tablet {
    width: 11.1111111111%;
  }

  .row > .column-1_10-tablet, .columns_wrap > .column-1_10-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-1_10-tablet {
    width: 10%;
  }

  .row > .column-1_11-tablet, .columns_wrap > .column-1_11-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-1_11-tablet {
    width: 9.0909090909%;
  }

  .row > .column-1_12-tablet, .columns_wrap > .column-1_12-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-1_12-tablet {
    width: 8.3333333333%;
  }

  .row > .column-2_2-tablet, .columns_wrap > .column-2_2-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-2_2-tablet {
    width: 100%;
  }

  .row > .column-2_3-tablet, .columns_wrap > .column-2_3-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-2_3-tablet {
    width: 66.6666666667%;
  }

  .row > .column-2_4-tablet, .columns_wrap > .column-2_4-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-2_4-tablet {
    width: 50%;
  }

  .row > .column-2_5-tablet, .columns_wrap > .column-2_5-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-2_5-tablet {
    width: 40%;
  }

  .row > .column-2_6-tablet, .columns_wrap > .column-2_6-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-2_6-tablet {
    width: 33.3333333333%;
  }

  .row > .column-2_7-tablet, .columns_wrap > .column-2_7-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-2_7-tablet {
    width: 28.5714285714%;
  }

  .row > .column-2_8-tablet, .columns_wrap > .column-2_8-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-2_8-tablet {
    width: 25%;
  }

  .row > .column-2_9-tablet, .columns_wrap > .column-2_9-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-2_9-tablet {
    width: 22.2222222222%;
  }

  .row > .column-2_10-tablet, .columns_wrap > .column-2_10-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-2_10-tablet {
    width: 20%;
  }

  .row > .column-2_11-tablet, .columns_wrap > .column-2_11-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-2_11-tablet {
    width: 18.1818181818%;
  }

  .row > .column-2_12-tablet, .columns_wrap > .column-2_12-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-2_12-tablet {
    width: 16.6666666667%;
  }

  .row > .column-3_3-tablet, .columns_wrap > .column-3_3-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-3_3-tablet {
    width: 100%;
  }

  .row > .column-3_4-tablet, .columns_wrap > .column-3_4-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-3_4-tablet {
    width: 75%;
  }

  .row > .column-3_5-tablet, .columns_wrap > .column-3_5-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-3_5-tablet {
    width: 60%;
  }

  .row > .column-3_6-tablet, .columns_wrap > .column-3_6-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-3_6-tablet {
    width: 50%;
  }

  .row > .column-3_7-tablet, .columns_wrap > .column-3_7-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-3_7-tablet {
    width: 42.8571428571%;
  }

  .row > .column-3_8-tablet, .columns_wrap > .column-3_8-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-3_8-tablet {
    width: 37.5%;
  }

  .row > .column-3_9-tablet, .columns_wrap > .column-3_9-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-3_9-tablet {
    width: 33.3333333333%;
  }

  .row > .column-3_10-tablet, .columns_wrap > .column-3_10-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-3_10-tablet {
    width: 30%;
  }

  .row > .column-3_11-tablet, .columns_wrap > .column-3_11-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-3_11-tablet {
    width: 27.2727272727%;
  }

  .row > .column-3_12-tablet, .columns_wrap > .column-3_12-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-3_12-tablet {
    width: 25%;
  }

  .row > .column-4_4-tablet, .columns_wrap > .column-4_4-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-4_4-tablet {
    width: 100%;
  }

  .row > .column-4_5-tablet, .columns_wrap > .column-4_5-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-4_5-tablet {
    width: 80%;
  }

  .row > .column-4_6-tablet, .columns_wrap > .column-4_6-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-4_6-tablet {
    width: 66.6666666667%;
  }

  .row > .column-4_7-tablet, .columns_wrap > .column-4_7-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-4_7-tablet {
    width: 57.1428571429%;
  }

  .row > .column-4_8-tablet, .columns_wrap > .column-4_8-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-4_8-tablet {
    width: 50%;
  }

  .row > .column-4_9-tablet, .columns_wrap > .column-4_9-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-4_9-tablet {
    width: 44.4444444444%;
  }

  .row > .column-4_10-tablet, .columns_wrap > .column-4_10-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-4_10-tablet {
    width: 40%;
  }

  .row > .column-4_11-tablet, .columns_wrap > .column-4_11-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-4_11-tablet {
    width: 36.3636363636%;
  }

  .row > .column-4_12-tablet, .columns_wrap > .column-4_12-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-4_12-tablet {
    width: 33.3333333333%;
  }

  .row > .column-5_5-tablet, .columns_wrap > .column-5_5-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-5_5-tablet {
    width: 100%;
  }

  .row > .column-5_6-tablet, .columns_wrap > .column-5_6-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-5_6-tablet {
    width: 83.3333333333%;
  }

  .row > .column-5_7-tablet, .columns_wrap > .column-5_7-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-5_7-tablet {
    width: 71.4285714286%;
  }

  .row > .column-5_8-tablet, .columns_wrap > .column-5_8-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-5_8-tablet {
    width: 62.5%;
  }

  .row > .column-5_9-tablet, .columns_wrap > .column-5_9-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-5_9-tablet {
    width: 55.5555555556%;
  }

  .row > .column-5_10-tablet, .columns_wrap > .column-5_10-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-5_10-tablet {
    width: 50%;
  }

  .row > .column-5_11-tablet, .columns_wrap > .column-5_11-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-5_11-tablet {
    width: 45.4545454545%;
  }

  .row > .column-5_12-tablet, .columns_wrap > .column-5_12-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-5_12-tablet {
    width: 41.6666666667%;
  }

  .row > .column-6_6-tablet, .columns_wrap > .column-6_6-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-6_6-tablet {
    width: 100%;
  }

  .row > .column-6_7-tablet, .columns_wrap > .column-6_7-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-6_7-tablet {
    width: 85.7142857143%;
  }

  .row > .column-6_8-tablet, .columns_wrap > .column-6_8-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-6_8-tablet {
    width: 75%;
  }

  .row > .column-6_9-tablet, .columns_wrap > .column-6_9-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-6_9-tablet {
    width: 66.6666666667%;
  }

  .row > .column-6_10-tablet, .columns_wrap > .column-6_10-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-6_10-tablet {
    width: 60%;
  }

  .row > .column-6_11-tablet, .columns_wrap > .column-6_11-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-6_11-tablet {
    width: 54.5454545455%;
  }

  .row > .column-6_12-tablet, .columns_wrap > .column-6_12-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-6_12-tablet {
    width: 50%;
  }

  .row > .column-7_7-tablet, .columns_wrap > .column-7_7-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-7_7-tablet {
    width: 100%;
  }

  .row > .column-7_8-tablet, .columns_wrap > .column-7_8-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-7_8-tablet {
    width: 87.5%;
  }

  .row > .column-7_9-tablet, .columns_wrap > .column-7_9-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-7_9-tablet {
    width: 77.7777777778%;
  }

  .row > .column-7_10-tablet, .columns_wrap > .column-7_10-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-7_10-tablet {
    width: 70%;
  }

  .row > .column-7_11-tablet, .columns_wrap > .column-7_11-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-7_11-tablet {
    width: 63.6363636364%;
  }

  .row > .column-7_12-tablet, .columns_wrap > .column-7_12-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-7_12-tablet {
    width: 58.3333333333%;
  }

  .row > .column-8_8-tablet, .columns_wrap > .column-8_8-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-8_8-tablet {
    width: 100%;
  }

  .row > .column-8_9-tablet, .columns_wrap > .column-8_9-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-8_9-tablet {
    width: 88.8888888889%;
  }

  .row > .column-8_10-tablet, .columns_wrap > .column-8_10-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-8_10-tablet {
    width: 80%;
  }

  .row > .column-8_11-tablet, .columns_wrap > .column-8_11-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-8_11-tablet {
    width: 72.7272727273%;
  }

  .row > .column-8_12-tablet, .columns_wrap > .column-8_12-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-8_12-tablet {
    width: 66.6666666667%;
  }

  .row > .column-9_9-tablet, .columns_wrap > .column-9_9-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-9_9-tablet {
    width: 100%;
  }

  .row > .column-9_10-tablet, .columns_wrap > .column-9_10-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-9_10-tablet {
    width: 90%;
  }

  .row > .column-9_11-tablet, .columns_wrap > .column-9_11-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-9_11-tablet {
    width: 81.8181818182%;
  }

  .row > .column-9_12-tablet, .columns_wrap > .column-9_12-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-9_12-tablet {
    width: 75%;
  }

  .row > .column-10_10-tablet, .columns_wrap > .column-10_10-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-10_10-tablet {
    width: 100%;
  }

  .row > .column-10_11-tablet, .columns_wrap > .column-10_11-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-10_11-tablet {
    width: 90.9090909091%;
  }

  .row > .column-10_12-tablet, .columns_wrap > .column-10_12-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-10_12-tablet {
    width: 83.3333333333%;
  }

  .row > .column-11_11-tablet, .columns_wrap > .column-11_11-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-11_11-tablet {
    width: 100%;
  }

  .row > .column-11_12-tablet, .columns_wrap > .column-11_12-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-11_12-tablet {
    width: 91.6666666667%;
  }

  .row > .column-12_12-tablet, .columns_wrap > .column-12_12-tablet,
  .trx_addons_columns_wrap > .trx_addons_column-12_12-tablet {
    width: 100%;
  }

  .row > .column-1-mobile, .columns_wrap > .column-1-mobile,
  .row > .column-1_1-mobile, .columns_wrap > .column-1_1-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-1-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-1_1-mobile {
    width: 100%;
  }

  .row > .column-1_2-mobile,
  .columns_wrap > .column-1_2-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-1_2-mobile {
    width: 50%;
  }

  .row > .column-1_3-mobile,
  .columns_wrap > .column-1_3-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-1_3-mobile {
    width: 33.3333333333%;
  }

  .row > .column-1_4-mobile,
  .columns_wrap > .column-1_4-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-1_4-mobile {
    width: 25%;
  }

  .row > .column-1_5-mobile,
  .columns_wrap > .column-1_5-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-1_5-mobile {
    width: 20%;
  }

  .row > .column-1_6-mobile,
  .columns_wrap > .column-1_6-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-1_6-mobile {
    width: 16.6666666667%;
  }

  .row > .column-1_7-mobile,
  .columns_wrap > .column-1_7-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-1_7-mobile {
    width: 14.2857142857%;
  }

  .row > .column-1_8-mobile,
  .columns_wrap > .column-1_8-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-1_8-mobile {
    width: 12.5%;
  }

  .row > .column-1_9-mobile,
  .columns_wrap > .column-1_9-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-1_9-mobile {
    width: 11.1111111111%;
  }

  .row > .column-1_10-mobile,
  .columns_wrap > .column-1_10-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-1_10-mobile {
    width: 10%;
  }

  .row > .column-1_11-mobile,
  .columns_wrap > .column-1_11-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-1_11-mobile {
    width: 9.0909090909%;
  }

  .row > .column-1_12-mobile,
  .columns_wrap > .column-1_12-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-1_12-mobile {
    width: 8.3333333333%;
  }

  .row > .column-2_2-mobile,
  .columns_wrap > .column-2_2-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-2_2-mobile {
    width: 100%;
  }

  .row > .column-2_3-mobile,
  .columns_wrap > .column-2_3-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-2_3-mobile {
    width: 66.6666666667%;
  }

  .row > .column-2_4-mobile,
  .columns_wrap > .column-2_4-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-2_4-mobile {
    width: 50%;
  }

  .row > .column-2_5-mobile,
  .columns_wrap > .column-2_5-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-2_5-mobile {
    width: 40%;
  }

  .row > .column-2_6-mobile,
  .columns_wrap > .column-2_6-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-2_6-mobile {
    width: 33.3333333333%;
  }

  .row > .column-2_7-mobile,
  .columns_wrap > .column-2_7-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-2_7-mobile {
    width: 28.5714285714%;
  }

  .row > .column-2_8-mobile,
  .columns_wrap > .column-2_8-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-2_8-mobile {
    width: 25%;
  }

  .row > .column-2_9-mobile,
  .columns_wrap > .column-2_9-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-2_9-mobile {
    width: 22.2222222222%;
  }

  .row > .column-2_10-mobile,
  .columns_wrap > .column-2_10-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-2_10-mobile {
    width: 20%;
  }

  .row > .column-2_11-mobile,
  .columns_wrap > .column-2_11-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-2_11-mobile {
    width: 18.1818181818%;
  }

  .row > .column-2_12-mobile,
  .columns_wrap > .column-2_12-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-2_12-mobile {
    width: 16.6666666667%;
  }

  .row > .column-3_3-mobile,
  .columns_wrap > .column-3_3-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-3_3-mobile {
    width: 100%;
  }

  .row > .column-3_4-mobile,
  .columns_wrap > .column-3_4-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-3_4-mobile {
    width: 75%;
  }

  .row > .column-3_5-mobile,
  .columns_wrap > .column-3_5-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-3_5-mobile {
    width: 60%;
  }

  .row > .column-3_6-mobile,
  .columns_wrap > .column-3_6-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-3_6-mobile {
    width: 50%;
  }

  .row > .column-3_7-mobile,
  .columns_wrap > .column-3_7-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-3_7-mobile {
    width: 42.8571428571%;
  }

  .row > .column-3_8-mobile,
  .columns_wrap > .column-3_8-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-3_8-mobile {
    width: 37.5%;
  }

  .row > .column-3_9-mobile,
  .columns_wrap > .column-3_9-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-3_9-mobile {
    width: 33.3333333333%;
  }

  .row > .column-3_10-mobile,
  .columns_wrap > .column-3_10-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-3_10-mobile {
    width: 30%;
  }

  .row > .column-3_11-mobile,
  .columns_wrap > .column-3_11-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-3_11-mobile {
    width: 27.2727272727%;
  }

  .row > .column-3_12-mobile,
  .columns_wrap > .column-3_12-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-3_12-mobile {
    width: 25%;
  }

  .row > .column-4_4-mobile,
  .columns_wrap > .column-4_4-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-4_4-mobile {
    width: 100%;
  }

  .row > .column-4_5-mobile,
  .columns_wrap > .column-4_5-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-4_5-mobile {
    width: 80%;
  }

  .row > .column-4_6-mobile,
  .columns_wrap > .column-4_6-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-4_6-mobile {
    width: 66.6666666667%;
  }

  .row > .column-4_7-mobile,
  .columns_wrap > .column-4_7-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-4_7-mobile {
    width: 57.1428571429%;
  }

  .row > .column-4_8-mobile,
  .columns_wrap > .column-4_8-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-4_8-mobile {
    width: 50%;
  }

  .row > .column-4_9-mobile,
  .columns_wrap > .column-4_9-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-4_9-mobile {
    width: 44.4444444444%;
  }

  .row > .column-4_10-mobile,
  .columns_wrap > .column-4_10-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-4_10-mobile {
    width: 40%;
  }

  .row > .column-4_11-mobile,
  .columns_wrap > .column-4_11-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-4_11-mobile {
    width: 36.3636363636%;
  }

  .row > .column-4_12-mobile,
  .columns_wrap > .column-4_12-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-4_12-mobile {
    width: 33.3333333333%;
  }

  .row > .column-5_5-mobile,
  .columns_wrap > .column-5_5-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-5_5-mobile {
    width: 100%;
  }

  .row > .column-5_6-mobile,
  .columns_wrap > .column-5_6-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-5_6-mobile {
    width: 83.3333333333%;
  }

  .row > .column-5_7-mobile,
  .columns_wrap > .column-5_7-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-5_7-mobile {
    width: 71.4285714286%;
  }

  .row > .column-5_8-mobile,
  .columns_wrap > .column-5_8-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-5_8-mobile {
    width: 62.5%;
  }

  .row > .column-5_9-mobile,
  .columns_wrap > .column-5_9-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-5_9-mobile {
    width: 55.5555555556%;
  }

  .row > .column-5_10-mobile,
  .columns_wrap > .column-5_10-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-5_10-mobile {
    width: 50%;
  }

  .row > .column-5_11-mobile,
  .columns_wrap > .column-5_11-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-5_11-mobile {
    width: 45.4545454545%;
  }

  .row > .column-5_12-mobile,
  .columns_wrap > .column-5_12-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-5_12-mobile {
    width: 41.6666666667%;
  }

  .row > .column-6_6-mobile,
  .columns_wrap > .column-6_6-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-6_6-mobile {
    width: 100%;
  }

  .row > .column-6_7-mobile,
  .columns_wrap > .column-6_7-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-6_7-mobile {
    width: 85.7142857143%;
  }

  .row > .column-6_8-mobile,
  .columns_wrap > .column-6_8-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-6_8-mobile {
    width: 75%;
  }

  .row > .column-6_9-mobile,
  .columns_wrap > .column-6_9-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-6_9-mobile {
    width: 66.6666666667%;
  }

  .row > .column-6_10-mobile,
  .columns_wrap > .column-6_10-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-6_10-mobile {
    width: 60%;
  }

  .row > .column-6_11-mobile,
  .columns_wrap > .column-6_11-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-6_11-mobile {
    width: 54.5454545455%;
  }

  .row > .column-6_12-mobile,
  .columns_wrap > .column-6_12-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-6_12-mobile {
    width: 50%;
  }

  .row > .column-7_7-mobile,
  .columns_wrap > .column-7_7-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-7_7-mobile {
    width: 100%;
  }

  .row > .column-7_8-mobile,
  .columns_wrap > .column-7_8-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-7_8-mobile {
    width: 87.5%;
  }

  .row > .column-7_9-mobile,
  .columns_wrap > .column-7_9-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-7_9-mobile {
    width: 77.7777777778%;
  }

  .row > .column-7_10-mobile,
  .columns_wrap > .column-7_10-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-7_10-mobile {
    width: 70%;
  }

  .row > .column-7_11-mobile,
  .columns_wrap > .column-7_11-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-7_11-mobile {
    width: 63.6363636364%;
  }

  .row > .column-7_12-mobile,
  .columns_wrap > .column-7_12-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-7_12-mobile {
    width: 58.3333333333%;
  }

  .row > .column-8_8-mobile,
  .columns_wrap > .column-8_8-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-8_8-mobile {
    width: 100%;
  }

  .row > .column-8_9-mobile,
  .columns_wrap > .column-8_9-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-8_9-mobile {
    width: 88.8888888889%;
  }

  .row > .column-8_10-mobile,
  .columns_wrap > .column-8_10-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-8_10-mobile {
    width: 80%;
  }

  .row > .column-8_11-mobile,
  .columns_wrap > .column-8_11-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-8_11-mobile {
    width: 72.7272727273%;
  }

  .row > .column-8_12-mobile,
  .columns_wrap > .column-8_12-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-8_12-mobile {
    width: 66.6666666667%;
  }

  .row > .column-9_9-mobile,
  .columns_wrap > .column-9_9-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-9_9-mobile {
    width: 100%;
  }

  .row > .column-9_10-mobile,
  .columns_wrap > .column-9_10-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-9_10-mobile {
    width: 90%;
  }

  .row > .column-9_11-mobile,
  .columns_wrap > .column-9_11-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-9_11-mobile {
    width: 81.8181818182%;
  }

  .row > .column-9_12-mobile,
  .columns_wrap > .column-9_12-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-9_12-mobile {
    width: 75%;
  }

  .row > .column-10_10-mobile,
  .columns_wrap > .column-10_10-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-10_10-mobile {
    width: 100%;
  }

  .row > .column-10_11-mobile,
  .columns_wrap > .column-10_11-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-10_11-mobile {
    width: 90.9090909091%;
  }

  .row > .column-10_12-mobile,
  .columns_wrap > .column-10_12-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-10_12-mobile {
    width: 83.3333333333%;
  }

  .row > .column-11_11-mobile,
  .columns_wrap > .column-11_11-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-11_11-mobile {
    width: 100%;
  }

  .row > .column-11_12-mobile,
  .columns_wrap > .column-11_12-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-11_12-mobile {
    width: 91.6666666667%;
  }

  .row > .column-12_12-mobile,
  .columns_wrap > .column-12_12-mobile,
  .trx_addons_columns_wrap > .trx_addons_column-12_12-mobile {
    width: 100%;
  }

  .row.columns_padding_bottom.columns_in_single_row > [class*="column-"][class*="-tablet"],
  .columns_wrap.columns_padding_bottom.columns_in_single_row > [class*="column-"][class*="-tablet"],
  .trx_addons_columns_wrap.columns_padding_bottom.columns_in_single_row > [class*="trx_addons_column-"][class*="-tablet"],
  .row.columns_padding_bottom.columns_in_single_row > [class*="column-"][class*="-mobile"],
  .columns_wrap.columns_padding_bottom.columns_in_single_row > [class*="column-"][class*="-mobile"],
  .trx_addons_columns_wrap.columns_padding_bottom.columns_in_single_row > [class*="trx_addons_column-"][class*="-mobile"] {
    padding-bottom: var(--theme-var-grid_gap);
  }

  /* WP Gallery Grid */
  .gallery.gallery-columns-9 .gallery-item {
    width: 33.3333% !important;
  }

  .gallery.gallery-columns-8 .gallery-item {
    width: 33.3333% !important;
  }

  .gallery.gallery-columns-7 .gallery-item {
    width: 33.3333% !important;
  }

  .gallery.gallery-columns-6 .gallery-item {
    width: 33.3333% !important;
  }

  .gallery.gallery-columns-5 .gallery-item {
    width: 33.3333% !important;
  }

  .gallery.gallery-columns-4 .gallery-item {
    width: 33.3333% !important;
  }

  img.alignleft, img.alignright,
  figure.alignleft, figure.alignright {
    float: none !important;
    display: block !important;
    margin: 1em 0 !important;
  }

  .wp-block-gallery.alignleft, .wp-block-gallery.aligncenter, .wp-block-gallery.alignright {
    display: flex !important;
  }

  /* Vertical menus */
  .sc_layouts_menu_dir_vertical .sc_layouts_menu_nav li.menu-item-has-children > a > .open_child_menu {
    display: block;
  }

  /* Header */
  .thumbnail_type_fullwidth .header_content_wrap .post_featured.post_featured_bg {
    height: 25rem;
  }

  .top_panel_default .top_panel_navi .columns_wrap > [class*="column-"] {
    width: 50%;
  }

  /* Core blocks */
  .sidebar_hide.narrow_content .alignleft,
  .sidebar_hide.narrow_content .alignright {
    max-width: none;
    float: none;
  }

  .sidebar_hide.narrow_content .alignleft {
    margin-left: 0 !important;
  }

  .sidebar_hide.narrow_content .alignright {
    margin-right: 0 !important;
  }

  .sidebar_hide.normal_content .alignleft,
  .sidebar_hide.normal_content .alignright {
    float: none;
  }

  .sidebar_hide.normal_content .alignleft {
    margin-left: 0 !important;
  }

  .sidebar_hide.normal_content .alignright {
    margin-right: 0 !important;
  }

  body.sidebar_hide.narrow_content .alignwide,
  body.sidebar_hide.normal_content .alignwide {
    left: 0;
    width: 100%;
  }

  /* Tags layouts */
  blockquote:not(.is-style-plain),
  blockquote[class*="wp-block-quote"][class*="is-style-"]:not(.is-style-plain),
  blockquote[class*="wp-block-quote"][class*="is-"]:not(.is-style-plain),
  .wp-block-quote:not(.is-large):not(.is-style-large):not(.is-style-plain),
  .wp-block-freeform.block-library-rich-text__tinymce blockquote:not(.is-style-plain) {
    padding: 2.2em 1.8em;
  }

  /* Page 404 */
  .post_item_404 .page_title {
    font-size: 10rem;
  }
  .post_item_404:not([class*="post_item_none_"]) .page_info {
    margin-top: 1.8rem;
  }
  .post_item_404 .page_description {
    margin-bottom: 1.5rem;
  }

  /* Page 'No search results' and 'No archive results' */
  .post_item_none_search .page_info, .post_item_none_search .search_wrap,
  .post_item_none_archive .page_info,
  .post_item_none_archive .search_wrap {
    max-width: 100%;
  }

  /* Widgets */
  [class*="content_wrap"] > .sidebar_default .widget {
    width: 100% !important;
  }
  [class*="content_wrap"] > .sidebar_default .widget + .widget,
  [class*="content_wrap"] > .sidebar_default .widget + .widget + .widget {
    margin-top: calc( var(--theme-var-grid_gap) * 1.25 );
  }

  /* Tags */
  .wp-block-tag-cloud a,
  .widget_product_tag_cloud a,
  .widget_tag_cloud a,
  .post_item_single .post_tags_single a {
    padding: 6px 14px;
  }

  /* Other minor plugins */
  .mfp-inline-holder .mfp-content,
  .mfp-ajax-holder .mfp-content {
    max-width: 100%;
    max-height: 100%;
  }

  .mfp-arrow-left {
    margin-top: -41px;
  }
}
@media (min-width: 600px) {
  /* WP Gallery Grid */
  .wp-block-gallery.columns-2 .blocks-gallery-image,
  .wp-block-gallery.columns-2 .blocks-gallery-item,
  .blocks-gallery-grid.columns-2 .blocks-gallery-image,
  .blocks-gallery-grid.columns-2 .blocks-gallery-item {
    width: calc(50% - 0.5001em);
  }

  .wp-block-gallery.columns-3 .blocks-gallery-image,
  .wp-block-gallery.columns-3 .blocks-gallery-item,
  .blocks-gallery-grid.columns-3 .blocks-gallery-image,
  .blocks-gallery-grid.columns-3 .blocks-gallery-item {
    width: calc(33.33333% - 0.6667em);
  }

  .wp-block-gallery.columns-4 .blocks-gallery-image,
  .wp-block-gallery.columns-4 .blocks-gallery-item,
  .blocks-gallery-grid.columns-4 .blocks-gallery-image,
  .blocks-gallery-grid.columns-4 .blocks-gallery-item {
    width: calc(25% - 0.7501em);
  }

  .wp-block-gallery.columns-5 .blocks-gallery-image,
  .wp-block-gallery.columns-5 .blocks-gallery-item,
  .blocks-gallery-grid.columns-5 .blocks-gallery-image,
  .blocks-gallery-grid.columns-5 .blocks-gallery-item {
    width: calc(20% - 0.8001em);
  }

  .wp-block-gallery.columns-6 .blocks-gallery-image,
  .wp-block-gallery.columns-6 .blocks-gallery-item,
  .blocks-gallery-grid.columns-6 .blocks-gallery-image,
  .blocks-gallery-grid.columns-6 .blocks-gallery-item {
    width: calc(16.66667% - 0.8334em);
  }

  .wp-block-gallery.columns-7 .blocks-gallery-image,
  .wp-block-gallery.columns-7 .blocks-gallery-item,
  .blocks-gallery-grid.columns-7 .blocks-gallery-image,
  .blocks-gallery-grid.columns-7 .blocks-gallery-item {
    width: calc(14.28571% - 0.8572em);
  }

  .wp-block-gallery.columns-8 .blocks-gallery-image,
  .wp-block-gallery.columns-8 .blocks-gallery-item,
  .blocks-gallery-grid.columns-8 .blocks-gallery-image,
  .blocks-gallery-grid.columns-8 .blocks-gallery-item {
    width: calc(12.5% - 0.8751em);
  }
}
@media (max-width: 600px) {
  /* WP Gallery Grid */
  .gallery.gallery-columns-9 .gallery-item {
    width: 50% !important;
  }

  .gallery.gallery-columns-8 .gallery-item {
    width: 50% !important;
  }

  .gallery.gallery-columns-7 .gallery-item {
    width: 50% !important;
  }

  .gallery.gallery-columns-6 .gallery-item {
    width: 50% !important;
  }

  .gallery.gallery-columns-5 .gallery-item {
    width: 50% !important;
  }

  .gallery.gallery-columns-4 .gallery-item {
    width: 50% !important;
  }

  .gallery.gallery-columns-3 .gallery-item {
    width: 50% !important;
  }
}
@media (max-width: 479px) {
  /* Theme vars */
  :root {
    --theme-var-size-koef: calc( 320 / 1680 );
    --theme-var-page_extra: 20px;
    --theme-var-page: calc( 100vw - var(--theme-var-page_extra) * 2 );
    --theme-var-sidebar: clamp( var(--theme-var-sidebar_width_min, 150px), calc( var(--theme-var-page) * var(--theme-var-sidebar_prc) * var(--theme-var-sidebar_proportional) + var(--theme-var-sidebar_width) * ( 1 - var(--theme-var-sidebar_proportional) ) ), var(--theme-var-sidebar_width_max, 500px) );
    --theme-var-sidebar_gap: clamp( var(--theme-var-sidebar_gap_width_min, 0px), calc( var(--theme-var-page) * var(--theme-var-sidebar_gap_prc) * var(--theme-var-sidebar_proportional) + var(--theme-var-sidebar_gap_width) * ( 1 - var(--theme-var-sidebar_proportional) ) ), var(--theme-var-sidebar_gap_width_max, 100px) );
    --theme-var-sidebar_and_gap: calc( var(--theme-var-sidebar) + var(--theme-var-sidebar_gap) );
    --theme-var-content: var(--theme-var-page);
    --theme-var-content_narrow: var(--theme-var-content);
    --theme-var-padding_narrow: 0px;
  }

  .body_style_boxed {
    --theme-var-page_boxed_extra: var(--theme-var-page_extra);
    --theme-var-page_boxed: 100vw;
    --theme-var-page: calc( var(--theme-var-page_boxed) - var(--theme-var-page_boxed_extra) * 2 );
    --theme-var-sidebar: clamp( var(--theme-var-sidebar_width_min, 150px), calc( var(--theme-var-page) * var(--theme-var-sidebar_prc) * var(--theme-var-sidebar_proportional) + var(--theme-var-sidebar_width) * ( 1 - var(--theme-var-sidebar_proportional) ) ), var(--theme-var-sidebar_width_max, 500px) );
    --theme-var-sidebar_gap: clamp( var(--theme-var-sidebar_gap_width_min, 0px), calc( var(--theme-var-page) * var(--theme-var-sidebar_gap_prc) * var(--theme-var-sidebar_proportional) + var(--theme-var-sidebar_gap_width) * ( 1 - var(--theme-var-sidebar_proportional) ) ), var(--theme-var-sidebar_gap_width_max, 100px) );
    --theme-var-sidebar_and_gap: calc( var(--theme-var-sidebar) + var(--theme-var-sidebar_gap) );
    --theme-var-content: var(--theme-var-page);
    --theme-var-content_narrow: var(--theme-var-content);
    --theme-var-padding_narrow: 0px;
  }

  .body_style_fullwide {
    --theme-var-page_fullwide_extra: var(--theme-var-page_extra);
    --theme-var-page: calc( 100vw - var(--theme-var-page_fullwide_extra) * 2 );
    --theme-var-sidebar: clamp( var(--theme-var-sidebar_width_min, 150px), calc( var(--theme-var-page) * var(--theme-var-sidebar_prc) * var(--theme-var-sidebar_proportional) + var(--theme-var-sidebar_width) * ( 1 - var(--theme-var-sidebar_proportional) ) ), var(--theme-var-sidebar_width_max, 500px) );
    --theme-var-sidebar_gap: clamp( var(--theme-var-sidebar_gap_width_min, 0px), calc( var(--theme-var-page) * var(--theme-var-sidebar_gap_prc) * var(--theme-var-sidebar_proportional) + var(--theme-var-sidebar_gap_width) * ( 1 - var(--theme-var-sidebar_proportional) ) ), var(--theme-var-sidebar_gap_width_max, 100px) );
    --theme-var-sidebar_and_gap: calc( var(--theme-var-sidebar) + var(--theme-var-sidebar_gap) );
    --theme-var-content: var(--theme-var-page);
    --theme-var-content_narrow: var(--theme-var-content);
    --theme-var-padding_narrow: 0px;
  }

  /* Theme Common styles */
  h1, h2, h3, h4, h5, h6 {
    hyphens: auto;
  }
}

/*# sourceMappingURL=responsive.css.map */
