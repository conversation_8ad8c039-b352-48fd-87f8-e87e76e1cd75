@import "../css/_mixins.scss";
@import "../css/_theme-vars.scss";
@import "../css/_admin-colors.scss";

#trx_addons_theme_panel_section_skins .trx_addons_image_block {
	&.skin_installed,
	&.skin_new,
	&.skin_updated {
		.trx_addons_image_block_image {
			overflow: hidden;
		}
		.skin_label {
			display: block;
			@include abs-rt(0, 0, 2000);
			font-size: 10px;
			font-weight: bold;
			text-transform: uppercase;
			background-color: #11a0d2;
			color: #fff;
			text-align: center;
			padding: 0 1em;
		}
	}
	&.skin_new .skin_label {
		background-color: #00a000;
	}
	&.skin_updated .skin_label {
		background-color: #a00000;
	}
}

#trx_addons_theme_panel_section_skins.trx_addons_section_mode_thumbs .trx_addons_image_block_inner {
	.trx_addons_image_block_image {
		height: 0;
		padding-top: 66.67%;
	}
	.trx_addons_image_block_title + .trx_addons_image_block_description {
		margin-top: 0.25em;
	}
}
#trx_addons_theme_panel_section_skins.trx_addons_section_mode_list {
	@media (min-width: 480px) {
		.trx_addons_image_block_wrap {
			@include flex-justify-content(space-between);
		}
		.trx_addons_image_block {
			width: 48% !important;
		}
		.trx_addons_image_block_inner {
			@include flex-direction(column);
		}
		.trx_addons_image_block_image {
			@include box(100%, 120px);
		}
		.trx_addons_image_block_footer {
			padding: 1em 0 0 0;

			.trx_addons_image_block_link {
				font-size: 14px;
			}
			.trx_addons_image_block_description {
				margin-bottom: 0.25em;
			}
			.trx_addons_image_block_link + .trx_addons_image_block_link {
				margin-top: 0;
			}
			.trx_addons_image_block_link_delete_skin {
				margin-top: 0;
				order: 5;
				color: #b33836;
			}
			.trx_addons_image_block_link_delete_skin:hover {
				color: #802826;
			}
		}		
	}
	@media (min-width: 768px) {
		.trx_addons_image_block_wrap {
			@include flex-justify-content(flex-start);
		}
		.trx_addons_image_block {
			width: 50% !important;
			padding: 0 1em 1em 0;
		}
		.trx_addons_image_block_inner {
			@include flex-direction(row);
		}
		.trx_addons_image_block_image {
			@include box(130px, 110px);
		}
		.trx_addons_image_block_footer {
			padding: 0 0 0 1em;
		}
	}
	@media (min-width: 1280px) {
		.trx_addons_image_block {
			width: 33.3333% !important;
		}
	}
	@media (min-width: 1440px) {
		.trx_addons_image_block {
			width: 25% !important;
		}
	}
	@media (min-width: 1680px) {
		.trx_addons_image_block {
			width: 20% !important;
			padding: 0 1.5em 1.5em 0;
		}
		.trx_addons_image_block_footer {
			padding-left: 1em;
		}
	}
}
.trx_addons_image_block_link_delete_skin {
	#trx_addons_theme_panel_section_skins.trx_addons_section_mode_list & span[data-tooltip-text],
	#trx_addons_theme_panel_section_skins.trx_addons_section_mode_thumbs & .trx_addons_image_block_link_caption {
		display: none;
	}
}

.elementra_upgrade_skins_button[disabled],
.elementra_upgrade_skins_button[disabled]:hover,
.elementra_upgrade_skins_button[disabled]:focus {
	color: #aaa;
	cursor: not-allowed;
}
.elementra_upgrade_skins_status_wrap {
	text-align: center;
	font-size: 1.5em;
}
.elementra_upgrade_skins_status:before {
	font-family: dashicons;	// $theme_icons;
	display: inline-block;
	vertical-align: top;
}
.elementra_upgrade_skins_status_progress:before {
	content: '\f463';	// '\e969';
	color: $accent_color;
	@include animation(spin 2s infinite linear);
}
.elementra_upgrade_skins_status_success:before {
	content: '\f147';	// '\e986';
	color: $success_color;
}
.elementra_upgrade_skins_status_error:before {
	content: '\f335';	// '\e987';
	color: $error_color;
}

/* Change proportions of the skins image */
#update-skins-table .plugins .plugin-title img {
	width: 94px;
	height: 64px;
}
