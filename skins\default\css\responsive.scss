@import "../../../css/_mixins.scss";
@import "../../../css/_theme-vars.scss";
@import "_skin-vars.scss";


//@mixin theme--xxl() {
@media #{$media_xxl} {
	/* Theme vars */
	:root {
		--theme-var-size-koef: calc( 1440 / 1680 );
		--theme-var-page: min( 1290px, var(--theme-var-page_width) );			// Page width
		@include page_dimensions;
	}

	.body_style_boxed {
		//--theme-var-page_boxed_extra: 40px;
		--theme-var-page_boxed_extra_new: calc( var(--theme-var-page_boxed_extra) * var(--theme-var-size-koef) );
		--theme-var-page_boxed: calc( var(--theme-var-page_width) + var(--theme-var-page_boxed_extra_new) * 2 );	// Width of the whole page (boxed)
		--theme-var-page: calc( var(--theme-var-page_boxed) - var(--theme-var-page_boxed_extra_new) * 2 );			// Page width
		@include page_dimensions;
	}
}


//@mixin theme--xl() {
@media #{$media_xl} {
	/* Theme vars */
	:root {
		--theme-var-size-koef: calc( 1280 / 1680 );
		--theme-var-page_width: 1100px;							// Width of the whole page
		--theme-var-page: var(--theme-var-page_width);			// Page width
		@include page_dimensions;

		// Skin specific vars (changeable)
		--theme-var-main_content_padding: 8rem;
	}

	.body_style_boxed {
		//--theme-var-page_boxed_extra: 40px;
		--theme-var-page_boxed_extra_new: calc( var(--theme-var-page_boxed_extra) * var(--theme-var-size-koef) );
		--theme-var-page_boxed: var(--theme-var-page_width);												// Width of the whole page (boxed)
		--theme-var-page: calc( var(--theme-var-page_boxed) - var(--theme-var-page_boxed_extra_new) * 2 );	// Page width
		@include page_dimensions;
	}
	.body_style_fullwide {
		--theme-var-page_fullwide_extra: 40px;
		--theme-var-page: calc( 100vw - var(--theme-var-page_fullwide_extra) * 2 );		// Page width
		@include page_dimensions;
	}
	
	/* Core blocks */
	body.sidebar_hide .alignwide,
	body.sidebar_hide.expand_content .alignwide {
		left: 0;
		width: 100%;
	}
}



//@mixin theme--lg() {
@media #{$media_lg} {
	/* Theme vars */
	:root {
		--theme-var-size-koef: calc( 1024 / 1680 );
		--theme-var-page_extra: 30px;												// Horizontal paddings of the wide and boxed pages
		--theme-var-page: calc( 100vw - var(--theme-var-page_extra) * 2 );			// Width of the whole page
		--theme-var-sidebar_width: 290px;											// Width of the sidebar (if not proportional option is used)
		--theme-var-sidebar_gap_width: 30px;										// Width of the gap between the content and sidebar (if not a proportional option is used)
		@include page_dimensions;

		// Skin specific vars (changeable)
		--theme-var-main_content_padding: 5.5rem;
		--theme-var-single_post_block_margin: 4.5rem;
	}

	.body_style_boxed {
		--theme-var-page_boxed_extra: var(--theme-var-page_extra);
		--theme-var-page_boxed: 100vw;																		// Width of the whole page (boxed)
		--theme-var-page: calc( var(--theme-var-page_boxed) - var(--theme-var-page_boxed_extra) * 2 );		// Page width
		@include page_dimensions;
	}
	.body_style_fullwide {
		--theme-var-page_fullwide_extra: var(--theme-var-page_extra);
		--theme-var-page: calc( 100vw - var(--theme-var-page_fullwide_extra) * 2 );	// Page width
		@include page_dimensions;
	}

	/* Columns, push, pull and offset sizes */
	@for $i from 1 through 12 {
		@for $j from $i through 12 {
			$s: 100% / $j * $i;
			@if $j == 1 {
				.column-#{$i}-tablet,
				.column-#{$i}_#{$j}-tablet,
				.trx_addons_column-#{$i}-tablet,
				.trx_addons_column-#{$i}_#{$j}-tablet {
					width: $s;
				}
			} @else {
				.column-#{$i}_#{$j}-tablet,
				.trx_addons_column-#{$i}_#{$j}-tablet {
					width: $s;
				}
			}
		}
	}
	.row.columns_padding_bottom.columns_in_single_row > [class*="column-"][class*="-tablet"],
	.columns_wrap.columns_padding_bottom.columns_in_single_row > [class*="column-"][class*="-tablet"],
	.trx_addons_columns_wrap.columns_padding_bottom.columns_in_single_row > [class*="trx_addons_column-"][class*="-tablet"] {
		padding-bottom: var(--theme-var-grid_gap);
	}

	/* Body sizes */
	.body_style_boxed .page_wrap {
		width: 100%;
	}
	.body_style_fullscreen.fixed_blocks_sticky:not(.elementor-editor-active) .sc_stack_section_effect_slide:not(.elementor-element-edit-mode) {
		top: 0;
	}

	/* Content and Sidebar */
	body:not(.expand_content) [class*="content_wrap"] > .content {
		width: 100% !important;
		float: none !important;
	}
	[class*="content_wrap"] > .sidebar {
		float: none !important;
		margin-top: var(--theme-var-main_content_padding);
	}
	[class*="content_wrap"] > .sidebar_default {
		margin-left: calc( ( var(--theme-var-grid_gap) / 2 ) * -1 );
		margin-right: calc( ( var(--theme-var-grid_gap) / 2 ) * -1 );
		width: calc( 100% + var(--theme-var-grid_gap) ) !important;
	}
	[class*="content_wrap"] > .sidebar_custom {
		width: 100% !important;
	}
	body.body_style_fullwide.sidebar_show [class*="content_wrap"] > .content,
	body.body_style_fullscreen.sidebar_show [class*="content_wrap"] > .content {
		padding-left: 0;
		padding-right: 0;
	}
	body.body_style_fullwide.sidebar_show [class*="content_wrap"] > .sidebar_default,
	body.body_style_fullscreen.sidebar_show [class*="content_wrap"] > .sidebar_default {
		margin-left: calc( ( var(--theme-var-grid_gap) /2 ) * -1 );
		margin-right: calc( ( var(--theme-var-grid_gap) / 2 ) * -1 );
	}

	/* Sticky sidebar */
	body.fixed_blocks_sticky .sidebar {
		position: static;
		top: auto !important;
	}

	/* Core blocks */
	body.sidebar_hide .alignfull {
		margin-left: calc( -100vw / 2 + 100% / 2 );
		margin-right: calc( -100vw / 2 + 100% / 2 );
		width: 100vw;
		max-width: 100vw;
	}
	.sidebar_hide.narrow_content .alignleft.is-style-alignfar,
	.sidebar_hide.narrow_content .is-style-alignfar > .alignleft,
	.sidebar_hide.narrow_content .alignright.is-style-alignfar,
	.sidebar_hide.narrow_content .is-style-alignfar > .alignright {
		max-width: calc( var(--theme-var-content_narrow) / 2 - var(--theme-var-grid_gap) );
	}

	/* Post layouts */
	.post_item .more-link {
		margin-top: 2em;
	}

	/* Widgets */
	[class*="content_wrap"] > .sidebar_default {
		.widget {
			display: inline-block;
			float: none;
			vertical-align: top;
			width: 50%;
			padding: 0 calc( var(--theme-var-grid_gap) / 2 );
			@include border-box;
			.widget {
				padding: 0;
			}
		}
		.widget + .widget {
			margin-top: 0;
		}
		.widget + .widget + .widget {
			margin-top: calc( var(--theme-var-grid_gap) * 1.25 );
		}
	}
	.widget.column-1_3, .widget.column-1_4, .widget.column-1_5, .widget.column-1_6, .widget.column-1_7, .widget.column-1_8, .widget.column-1_9, .widget.column-1_10, .widget.column-1_11, .widget.column-1_12 {
		width: 50%;
	}

}


//@mixin theme--md() {
@media #{$media_md} {
	/* Theme vars */
	:root {
		--theme-var-size-koef: calc( 768 / 1680 );
		--theme-var-page_extra: 30px;																		// Horizontal paddings of the wide and boxed pages
		--theme-var-page: calc( 100vw - var(--theme-var-page_extra) * 2 );									// Width of the whole page
		@include page_dimensions(1);
	}
	.body_style_boxed {
		--theme-var-page_boxed_extra: var(--theme-var-page_extra);
		--theme-var-page_boxed: 100vw;																		// Width of the whole page (boxed)
		--theme-var-page: calc( var(--theme-var-page_boxed) - var(--theme-var-page_boxed_extra) * 2 );		// Page width
		@include page_dimensions(1);
	}
	.body_style_fullwide {
		--theme-var-page_fullwide_extra: var(--theme-var-page_extra);
		--theme-var-page: calc( 100vw - var(--theme-var-page_fullwide_extra) * 2 );							// Page width
		@include page_dimensions(1);
	}

	/* Leave max 5 columns in the gallery */
	.gallery.gallery-columns-9 .gallery-item {	width: 20% !important; }
	.gallery.gallery-columns-8 .gallery-item {	width: 20% !important; }
	.gallery.gallery-columns-7 .gallery-item {	width: 20% !important; }
	.gallery.gallery-columns-6 .gallery-item {	width: 20% !important; }

}


//@mixin theme--sm() {
@media #{$media_sm} {
	/* Theme vars */
	:root {
		--theme-var-size-koef: calc( 480 / 1680 );
		--theme-var-page_extra: 20px;																		// Horizontal paddings of the wide and boxed pages
		--theme-var-page: calc( 100vw - var(--theme-var-page_extra) * 2 );									// Width of the whole page
		--theme-var-grid_gap: 20px;																			// Gap between columns in the grid
		--theme-var-grid_max_columns: 1;																	// Max columns in the row of the grid
		@include page_dimensions(1, 1);

		// Skin specific vars (changeable)
		--theme-var-main_content_padding: 3.2rem;
		--theme-var-single_post_block_margin: 3rem;
	}
	.body_style_boxed {
		--theme-var-page_boxed_extra: var(--theme-var-page_extra);
		--theme-var-page_boxed: 100vw;																		// Width of the whole page (boxed)
		--theme-var-page: calc( var(--theme-var-page_boxed) - var(--theme-var-page_boxed_extra) * 2 );		// Page width
		@include page_dimensions(1, 1);
	}
	.body_style_fullwide {
		--theme-var-page_fullwide_extra: var(--theme-var-page_extra);
		--theme-var-page: calc( 100vw - var(--theme-var-page_fullwide_extra) * 2 );							// Page width
		@include page_dimensions(1, 1);
	}

	/* Content and Sidebar */
	body:not(.expand_content):not(.body_style_fullwide):not(.body_style_fullscreen) [class*="content_wrap"] > .content {
		margin-bottom: 0;
	}

	/* Tags layouts */
	.wp-block-table, table {
		td, th {
			padding: 0.6rem;
		}
	}

	/* Grid */
	.row.columns_padding_bottom.columns_in_single_row > [class*="column-"],
	.columns_wrap.columns_padding_bottom.columns_in_single_row > [class*="column-"],
	.trx_addons_columns_wrap.columns_padding_bottom.columns_in_single_row > [class*="trx_addons_column-"] {
		padding-bottom: var(--theme-var-grid_gap);
	}
	.row.columns_padding_bottom.columns_in_single_row > [class*="column-"]:last-child,
	.columns_wrap.columns_padding_bottom.columns_in_single_row > [class*="column-"]:last-child,
	.trx_addons_columns_wrap.columns_padding_bottom.columns_in_single_row > [class*="trx_addons_column-"]:last-child {
		padding-bottom: 0;
	}

	.row:not(.columns_padding_bottom) > [class*="column-"]:nth-child(2n+3),
	.columns_wrap:not(.columns_padding_bottom) > [class*="column-"]:nth-child(2n+3),
	.row:not(.columns_padding_bottom) > [class*="column-"]:nth-child(2n+4),
	.columns_wrap:not(.columns_padding_bottom) > [class*="column-"]:nth-child(2n+4),
	.trx_addons_columns_wrap:not(.columns_padding_bottom) > [class*="trx_addons_column-"]:nth-child(2n+3),
	.trx_addons_columns_wrap:not(.columns_padding_bottom) > [class*="trx_addons_column-"]:nth-child(2n+4) {
		padding-top: var(--theme-var-grid_gap);
	}

	/* Max column's width in the row */
	.row > [class*="column-"],
	.columns_wrap > [class*="column-"],
	.trx_addons_columns_wrap > [class*="trx_addons_column-"] {
		width: calc( 100% / var(--theme-var-grid_max_columns) );
	}
	.row > .column-1_1, .row > .column-2_2, .row > .column-3_3, .row > .column-4_4,
	.row > .column-5_5,	.row > .column-6_6, .row > .column-7_7, .row > .column-8_8,
	.row > .column-9_9, .row > .column-10_10,
	.row > .column-11_11, .row > .column-12_12,
	.columns_wrap > .column-1_1, .columns_wrap > .column-2_2, .columns_wrap > .column-3_3,
	.columns_wrap > .column-4_4, .columns_wrap > .column-5_5, .columns_wrap > .column-6_6,
	.columns_wrap > .column-7_7, .columns_wrap > .column-8_8, .columns_wrap > .column-9_9,
	.columns_wrap > .column-10_10, .columns_wrap > .column-11_11, .columns_wrap > .column-12_12,
/* Uncomments next selector if you want to stretch each 3-th column to 100% */
/*
	.row > .column-1_3:nth-child(3n), .columns_wrap > .column-1_3:nth-child(3n),
*/
	.row > .column-2_3, .columns_wrap > .column-2_3,
	.row > .column-1_3.after_span_2, .columns_wrap > .column-1_3.after_span_2,
	.row > .column-2_4, .columns_wrap > .column-3_4,
	.row > .column-1_4.after_span_2, .columns_wrap > .column-1_4.after_span_3,
/* Uncomments next selector if you want to stretch each 5-th column to 100% */
/*
	.row > .column-1_5:nth-child(5n), .columns_wrap > .column-1_5:nth-child(5n),
*/
	.row > .column-2_5, .columns_wrap > .column-2_5,
	.row > .column-3_5, .columns_wrap > .column-3_5,
	.row > .column-4_5, .columns_wrap > .column-4_5,
	.row > .column-2_6, .columns_wrap > .column-2_6,
	.row > .column-3_6, .columns_wrap > .column-3_6,
	.row > .column-4_6, .columns_wrap > .column-4_6,
	.row > .column-5_6, .columns_wrap > .column-5_6,
	.trx_addons_columns_wrap > .trx_addons_column-1_1, .trx_addons_columns_wrap > .trx_addons_column-2_2,
	.trx_addons_columns_wrap > .trx_addons_column-3_3, .trx_addons_columns_wrap > .trx_addons_column-4_4,
	.trx_addons_columns_wrap > .trx_addons_column-5_5, .trx_addons_columns_wrap > .trx_addons_column-6_6,
	.trx_addons_columns_wrap > .trx_addons_column-7_7, .trx_addons_columns_wrap > .trx_addons_column-8_8,
	.trx_addons_columns_wrap > .trx_addons_column-9_9, .trx_addons_columns_wrap > .trx_addons_column-10_10,
	.trx_addons_columns_wrap > .trx_addons_column-11_11,.trx_addons_columns_wrap > .trx_addons_column-12_12,
/* Uncomments next selector if you want to stretch each 3-th column to 100% */
/*
	.trx_addons_columns_wrap > .trx_addons_column-1_3:nth-child(3n),
*/
	.trx_addons_columns_wrap > .trx_addons_column-2_3,
	.trx_addons_columns_wrap > .trx_addons_column-1_3.after_span_2,
	.trx_addons_columns_wrap > .trx_addons_column-3_4,
	.trx_addons_columns_wrap > .trx_addons_column-1_4.after_span_3,
/* Uncomments next selector if you want to stretch each 5-th column to 100% */
/*
	.trx_addons_columns_wrap > .trx_addons_column-1_5:nth-child(5n),
*/
	.trx_addons_columns_wrap > .trx_addons_column-2_5,
	.trx_addons_columns_wrap > .trx_addons_column-3_5,
	.trx_addons_columns_wrap > .trx_addons_column-4_5,
	.trx_addons_columns_wrap > .trx_addons_column-2_6,
	.trx_addons_columns_wrap > .trx_addons_column-3_6,
	.trx_addons_columns_wrap > .trx_addons_column-4_6,
	.trx_addons_columns_wrap > .trx_addons_column-5_6 {
		width: 100%;
	}

	/* Columns, push, pull and offset sizes */
	@for $i from 1 through 12 {
		@for $j from $i through 12 {
			$s: 100% / $j * $i;
			@if $j == 1 {
				.row > .column-#{$i}-tablet, .columns_wrap > .column-#{$i}-tablet,
				.row > .column-#{$i}_#{$j}-tablet, .columns_wrap > .column-#{$i}_#{$j}-tablet,
				.trx_addons_columns_wrap > .trx_addons_column-#{$i}-tablet,
				.trx_addons_columns_wrap > .trx_addons_column-#{$i}_#{$j}-tablet {
					width: $s;
				}
			} @else {
				.row > .column-#{$i}_#{$j}-tablet, .columns_wrap > .column-#{$i}_#{$j}-tablet,
				.trx_addons_columns_wrap > .trx_addons_column-#{$i}_#{$j}-tablet {
					width: $s;
				}
			}
		}
	}
	@for $i from 1 through 12 {
		@for $j from $i through 12 {
			$s: 100% / $j * $i;
			@if $j == 1 {
				.row > .column-#{$i}-mobile, .columns_wrap > .column-#{$i}-mobile,
				.row > .column-#{$i}_#{$j}-mobile, .columns_wrap > .column-#{$i}_#{$j}-mobile,
				.trx_addons_columns_wrap > .trx_addons_column-#{$i}-mobile,
				.trx_addons_columns_wrap > .trx_addons_column-#{$i}_#{$j}-mobile {
					width: $s;
				}
			} @else {
				.row > .column-#{$i}_#{$j}-mobile,
				.columns_wrap > .column-#{$i}_#{$j}-mobile,
				.trx_addons_columns_wrap > .trx_addons_column-#{$i}_#{$j}-mobile {
					width: $s;
				}
			}
		}
	}
	.row.columns_padding_bottom.columns_in_single_row > [class*="column-"][class*="-tablet"],
	.columns_wrap.columns_padding_bottom.columns_in_single_row > [class*="column-"][class*="-tablet"],
	.trx_addons_columns_wrap.columns_padding_bottom.columns_in_single_row > [class*="trx_addons_column-"][class*="-tablet"],
	.row.columns_padding_bottom.columns_in_single_row > [class*="column-"][class*="-mobile"],
	.columns_wrap.columns_padding_bottom.columns_in_single_row > [class*="column-"][class*="-mobile"],
	.trx_addons_columns_wrap.columns_padding_bottom.columns_in_single_row > [class*="trx_addons_column-"][class*="-mobile"] {
		padding-bottom: var(--theme-var-grid_gap);
	}

	/* WP Gallery Grid */
	.gallery.gallery-columns-9 .gallery-item {	width: 33.3333% !important; }
	.gallery.gallery-columns-8 .gallery-item {	width: 33.3333% !important; }
	.gallery.gallery-columns-7 .gallery-item {	width: 33.3333% !important; }
	.gallery.gallery-columns-6 .gallery-item {	width: 33.3333% !important; }
	.gallery.gallery-columns-5 .gallery-item {	width: 33.3333% !important; }
	.gallery.gallery-columns-4 .gallery-item {	width: 33.3333% !important; }
	
	img.alignleft, img.alignright,
	figure.alignleft, figure.alignright {
		float: none !important;
		display: block !important;
		margin: 1em 0 !important;
	}
	.wp-block-gallery.alignleft, .wp-block-gallery.aligncenter, .wp-block-gallery.alignright {
		display: flex !important;
	}

	/* Vertical menus */
	.sc_layouts_menu_dir_vertical .sc_layouts_menu_nav li.menu-item-has-children > a > .open_child_menu {
		display: block;
	}
	
	/* Header */
	.thumbnail_type_fullwidth .header_content_wrap .post_featured.post_featured_bg {
		height: 25rem;
	}
	.top_panel_default .top_panel_navi .columns_wrap > [class*="column-"] {
		width: 50%;
	}
	
	/* Core blocks */
	.sidebar_hide.narrow_content .alignleft,
	.sidebar_hide.narrow_content .alignright {
		max-width: none;
		float: none;
	}
	.sidebar_hide.narrow_content .alignleft {
		margin-left: 0 !important;
	}
	.sidebar_hide.narrow_content .alignright {
		margin-right: 0 !important;
	}
	.sidebar_hide.normal_content .alignleft,
	.sidebar_hide.normal_content .alignright {
		float: none;
	}
	.sidebar_hide.normal_content .alignleft {
		margin-left: 0 !important;
	}
	.sidebar_hide.normal_content .alignright {
		margin-right: 0 !important;
	}
	body.sidebar_hide.narrow_content .alignwide,
	body.sidebar_hide.normal_content .alignwide {
//		Way 1: .alignwide blocks are aligned at the left side of the content
		left: 0;
		width: 100%;
	}

	/* Tags layouts */
	blockquote,
	blockquote[class*="wp-block-quote"][class*="is-style-"],
	blockquote[class*="wp-block-quote"][class*="is-"],
	.wp-block-quote:not(.is-large):not(.is-style-large),
	.wp-block-freeform.block-library-rich-text__tinymce blockquote {
		&:not(.is-style-plain) {
			padding: 2.2em 1.8em;
		}
	}

	/* Page 404 */
	.post_item_404 {
		.page_title {
			font-size: 10rem;
		}
		&:not([class*="post_item_none_"]) .page_info {
			margin-top: 1.8rem;
		}
		.page_description {
			margin-bottom: 1.5rem;
		}
	}
	/* Page 'No search results' and 'No archive results' */
	.post_item_none_search,
	.post_item_none_archive {
		.page_info, .search_wrap {
			max-width: 100%;
		}
	}

	/* Widgets */
	[class*="content_wrap"] > .sidebar_default .widget {
		width: 100% !important;
		+ .widget,
		+ .widget + .widget {
			margin-top: calc( var(--theme-var-grid_gap) * 1.25 );
		}
	}
	/* Tags */
	.wp-block-tag-cloud a,
	.widget_product_tag_cloud a, 
	.widget_tag_cloud a,
	.post_item_single .post_tags_single a {
		padding: 6px 14px;
	}

	/* Other minor plugins */
	.mfp-inline-holder .mfp-content,
	.mfp-ajax-holder .mfp-content {
		max-width: 100%;
		max-height: 100%;
	}
	.mfp-arrow-left {
		margin-top: -41px;
	}
}


//@mixin theme--wp_over() {
@media #{$media_wp_over} {
	/* WP Gallery Grid */
	.wp-block-gallery.columns-2 .blocks-gallery-image,
	.wp-block-gallery.columns-2 .blocks-gallery-item,
	.blocks-gallery-grid.columns-2 .blocks-gallery-image,
	.blocks-gallery-grid.columns-2 .blocks-gallery-item {
		width: calc(50% - 0.5001em);
	}
	.wp-block-gallery.columns-3 .blocks-gallery-image,
	.wp-block-gallery.columns-3 .blocks-gallery-item,
	.blocks-gallery-grid.columns-3 .blocks-gallery-image,
	.blocks-gallery-grid.columns-3 .blocks-gallery-item {
		width: calc(33.33333% - 0.6667em);
	}
	.wp-block-gallery.columns-4 .blocks-gallery-image,
	.wp-block-gallery.columns-4 .blocks-gallery-item,
	.blocks-gallery-grid.columns-4 .blocks-gallery-image,
	.blocks-gallery-grid.columns-4 .blocks-gallery-item {
		width: calc(25% - 0.7501em);
	}
	.wp-block-gallery.columns-5 .blocks-gallery-image,
	.wp-block-gallery.columns-5 .blocks-gallery-item,
	.blocks-gallery-grid.columns-5 .blocks-gallery-image,
	.blocks-gallery-grid.columns-5 .blocks-gallery-item {
		width: calc(20% - 0.8001em);
	}
	.wp-block-gallery.columns-6 .blocks-gallery-image,
	.wp-block-gallery.columns-6 .blocks-gallery-item,
	.blocks-gallery-grid.columns-6 .blocks-gallery-image,
	.blocks-gallery-grid.columns-6 .blocks-gallery-item {
		width: calc(16.66667% - 0.8334em);
	}
	.wp-block-gallery.columns-7 .blocks-gallery-image,
	.wp-block-gallery.columns-7 .blocks-gallery-item,
	.blocks-gallery-grid.columns-7 .blocks-gallery-image,
	.blocks-gallery-grid.columns-7 .blocks-gallery-item {
		width: calc(14.28571% - 0.8572em);
	}
	.wp-block-gallery.columns-8 .blocks-gallery-image,
	.wp-block-gallery.columns-8 .blocks-gallery-item,
	.blocks-gallery-grid.columns-8 .blocks-gallery-image,
	.blocks-gallery-grid.columns-8 .blocks-gallery-item {
		width: calc(12.5% - 0.8751em);
	}
}


//@mixin theme--sm_wp() {
@media #{$media_sm_wp} {
	/* WP Gallery Grid */
	.gallery.gallery-columns-9 .gallery-item {	width: 50% !important; }
	.gallery.gallery-columns-8 .gallery-item {	width: 50% !important; }
	.gallery.gallery-columns-7 .gallery-item {	width: 50% !important; }
	.gallery.gallery-columns-6 .gallery-item {	width: 50% !important; }
	.gallery.gallery-columns-5 .gallery-item {	width: 50% !important; }
	.gallery.gallery-columns-4 .gallery-item {	width: 50% !important; }
	.gallery.gallery-columns-3 .gallery-item {	width: 50% !important; }
}


//@mixin theme--xs() {
@media #{$media_xs} {

	/* Theme vars */
	:root {
		--theme-var-size-koef: calc( 320 / 1680 );
		--theme-var-page_extra: 20px;																		// Horizontal paddings of the wide and boxed pages
		--theme-var-page: calc( 100vw - var(--theme-var-page_extra) * 2 );									// Width of the whole page
		@include page_dimensions(1, 1);
	}
	.body_style_boxed {
		--theme-var-page_boxed_extra: var(--theme-var-page_extra);
		--theme-var-page_boxed: 100vw;																		// Width of the whole page (boxed)
		--theme-var-page: calc( var(--theme-var-page_boxed) - var(--theme-var-page_boxed_extra) * 2 );		// Page width
		@include page_dimensions(1, 1);
	}
	.body_style_fullwide {
		--theme-var-page_fullwide_extra: var(--theme-var-page_extra);
		--theme-var-page: calc( 100vw - var(--theme-var-page_fullwide_extra) * 2 );							// Page width
		@include page_dimensions(1, 1);
	}

	/* Theme Common styles */
	h1, h2, h3, h4, h5, h6 {
		hyphens: auto;
	}
}