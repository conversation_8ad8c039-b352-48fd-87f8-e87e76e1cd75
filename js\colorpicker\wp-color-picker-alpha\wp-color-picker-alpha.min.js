(function(c,b){var e={version:303};if("wpColorPickerAlpha" in window&&"version" in window.wpColorPickerAlpha){var a=parseInt(window.wpColorPickerAlpha.version,10);if(!isNaN(a)&&a>=e.version){return}}if(Color.fn.hasOwnProperty("to_s")){return}Color.fn.to_s=function(g){g=(g||"hex");if("hex"===g&&this._alpha<1){g="rgba"}var f="";if("hex"===g){f=this.toString()}else{if(!this.error){f=this.toCSS(g).replace(/\(\s+/,"(").replace(/\s+\)/,")")}}return f};window.wpColorPickerAlpha=e;var d="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAIAAAHnlligAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAHJJREFUeNpi+P///4EDBxiAGMgCCCAGFB5AADGCRBgYDh48CCRZIJS9vT2QBAggFBkmBiSAogxFBiCAoHogAKIKAlBUYTELAiAmEtABEECk20G6BOmuIl0CIMBQ/IEMkO0myiSSraaaBhZcbkUOs0HuBwDplz5uFJ3Z4gAAAABJRU5ErkJggg==";c.widget("a8c.iris",c.a8c.iris,{alphaOptions:{alphaEnabled:false},_getColor:function(f){if(f===b){f=this._color}if(this.alphaOptions.alphaEnabled){f=f.to_s(this.alphaOptions.alphaColorType);if(!this.alphaOptions.alphaColorWithSpace){f=f.replace(/\s+/g,"")}return f}return f.toString()},_create:function(){try{this.alphaOptions=this.element.wpColorPicker("instance").alphaOptions}catch(f){}c.extend({},this.alphaOptions,{alphaEnabled:false,alphaCustomWidth:130,alphaReset:false,alphaColorType:"hex",alphaColorWithSpace:false,alphaSkipDebounce:false,alphaDebounceTimeout:100});this._super()},_addInputListeners:function(g){var f=this,h=function(k){var l=g.val(),i=new Color(l),l=l.replace(/^(#|(rgb|hsl)a?)/,""),j=f.alphaOptions.alphaColorType;g.removeClass("iris-error");if(!i.error){if("hex"!==j||!(k.type==="keyup"&&l.match(/^[0-9a-fA-F]{3}$/))){if(i.toIEOctoHex()!==f._color.toIEOctoHex()){f._setOption("color",f._getColor(i))}}}else{if(l!==""){g.addClass("iris-error")}}};g.on("change",h);if(!f.alphaOptions.alphaSkipDebounce){g.on("keyup",f._debounce(h,f.alphaOptions.alphaDebounceTimeout))}if(f.options.hide){g.one("focus",function(){f.show()})}},_initControls:function(){this._super();if(this.alphaOptions.alphaEnabled){var g=this,i=g.controls.strip.clone(false,false),h=i.find(".iris-slider-offset"),f={stripAlpha:i,stripAlphaSlider:h};i.addClass("iris-strip-alpha");h.addClass("iris-slider-offset-alpha");i.appendTo(g.picker.find(".iris-picker-inner"));c.each(f,function(l,j){g.controls[l]=j});g.controls.stripAlphaSlider.slider({orientation:"vertical",min:0,max:100,step:1,value:parseInt(g._color._alpha*100),slide:function(j,k){g.active="strip";g._color._alpha=parseFloat(k.value/100);g._change.apply(g,arguments)}})}},_dimensions:function(l){this._super(l);if(this.alphaOptions.alphaEnabled){var p=this,f=p.options,n=p.controls,o=n.square,g=p.picker.find(".iris-strip"),m,j,i,h,k;m=Math.round(p.picker.outerWidth(true)-(f.border?22:0));j=Math.round(o.outerWidth());i=Math.round((m-j)/2);h=Math.round(i/2);k=Math.round(j+(i*2)+(h*2));while(k>m){i=Math.round(i-2);h=Math.round(h-1);k=Math.round(j+(i*2)+(h*2))}o.css("margin","0");g.width(i).css("margin-left",h+"px")}},_change:function(){this._super();if(this.alphaOptions.alphaEnabled){var f=this,g=f.active;controls=f.controls,alpha=parseInt(f._color._alpha*100),color=f._color.toRgb(),gradient=["rgb("+color.r+","+color.g+","+color.b+") 0%","rgba("+color.r+","+color.g+","+color.b+", 0) 100%"];f.options.color=f._getColor();controls.stripAlpha.css({background:"linear-gradient(to bottom, "+gradient.join(", ")+"), url("+d+")"});if(g){controls.stripAlphaSlider.slider("value",alpha)}if(!f._color.error){f.element.removeClass("iris-error").val(f.options.color)}f.picker.find(".iris-palette-container").on("click.palette",".iris-palette",function(){var h=c(this).data("color");if(f.alphaOptions.alphaReset){f._color._alpha=1;h=f._getColor()}f._setOption("color",h)})}},_paintDimension:function(h,i){var g=this,f=false;if(g.alphaOptions.alphaEnabled&&"strip"===i){f=g._color;g._color=new Color(f.toString());g.hue=g._color.h()}g._super(h,i);if(f){g._color=f}},_setOption:function(g,h){var f=this;if("color"===g&&f.alphaOptions.alphaEnabled){h=""+h;newColor=new Color(h).setHSpace(f.options.mode);if(!newColor.error&&f._getColor(newColor)!==f._getColor()){f._color=newColor;f.options.color=f._getColor();f.active="external";f._change()}}else{return f._super(g,h)}},color:function(f){if(f===true){return this._color.clone()}if(f===b){return this._getColor()}this.option("color",f)}});c.widget("wp.wpColorPicker",c.wp.wpColorPicker,{alphaOptions:{alphaEnabled:false},_getAlphaOptions:function(){var i=this.element,h=(i.data("type")||this.options.type),f=(i.data("defaultColor")||i.val()),g={alphaEnabled:(i.data("alphaEnabled")||false),alphaCustomWidth:130,alphaReset:false,alphaColorType:"rgb",alphaColorWithSpace:false,alphaSkipDebounce:(!!i.data("alphaSkipDebounce")||false)};if(g.alphaEnabled){g.alphaEnabled=(i.is("input")&&"full"===h)}if(!g.alphaEnabled){return g}g.alphaColorWithSpace=(f&&f.match(/\s/));c.each(g,function(k,j){var l=(i.data(k)||j);switch(k){case"alphaCustomWidth":l=(l?parseInt(l,10):0);l=(isNaN(l)?j:l);break;case"alphaColorType":if(!l.match(/^(hex|(rgb|hsl)a?)$/)){if(f&&f.match(/^#/)){l="hex"}else{if(f&&f.match(/^hsla?/)){l="hsl"}else{l=j}}}break;default:l=!!l;break}g[k]=l});return g},_create:function(){if(!c.support.iris){return}this.alphaOptions=this._getAlphaOptions();this._super()},_addListeners:function(){if(!this.alphaOptions.alphaEnabled){return this._super()}var f=this,g=f.element,h=f.toggler.is("a");this.alphaOptions.defaultWidth=g.width();if(this.alphaOptions.alphaCustomWidth){g.width(parseInt(this.alphaOptions.defaultWidth+this.alphaOptions.alphaCustomWidth,10))}f.toggler.css({position:"relative","background-image":"url("+d+")"});if(h){f.toggler.html('<span class="color-alpha" />')}else{f.toggler.append('<span class="color-alpha" />')}f.colorAlpha=f.toggler.find("span.color-alpha").css({width:"30px",height:"100%",position:"absolute",top:0,"background-color":g.val()});if("ltr"===f.colorAlpha.css("direction")){f.colorAlpha.css({"border-bottom-left-radius":"2px","border-top-left-radius":"2px",left:0})}else{f.colorAlpha.css({"border-bottom-right-radius":"2px","border-top-right-radius":"2px",right:0})}g.iris({change:function(j,k){f.colorAlpha.css({"background-color":k.color.to_s(f.alphaOptions.alphaColorType)});if(typeof f.options.change==="function"){f.options.change.call(this,j,k)}if(wp&&wp.customize){var i=f.element.parents(".customize-control-color");if(i.length){wp.customize(i.attr("id").replace("customize-control-","")).set(k.color.to_s(f.alphaOptions.alphaColorType))}}}});f.wrap.on("click.wpcolorpicker",function(i){i.stopPropagation()});f.toggler.on("click",function(){if(f.toggler.hasClass("wp-picker-open")){f.close()}else{f.open()}});g.on("change",function(i){var j=c(this).val();if(g.hasClass("iris-error")||j===""||j.match(/^(#|(rgb|hsl)a?)$/)){if(h){f.toggler.removeAttr("style")}f.colorAlpha.css("background-color","");if(typeof f.options.clear==="function"){f.options.clear.call(this,i)}}});f.button.on("click",function(i){var j=c(this);if(j.hasClass("wp-picker-default")){g.val(f.options.defaultColor).change()}else{if(j.hasClass("wp-picker-clear")){g.val("");if(h){f.toggler.removeAttr("style")}f.colorAlpha.css("background-color","");if(typeof f.options.clear==="function"){f.options.clear.call(this,i)}g.trigger("change")}}})}})})(jQuery);