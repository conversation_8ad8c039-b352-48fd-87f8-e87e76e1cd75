<svg viewBox="0 0 32 32" width="32" height="32">
	<style>
		#preloader_spinner {
			box-sizing: border-box;
			stroke: #673AB7;
			stroke-width: 3px;
			transform-origin: 50%;
			-webkit-animation: preloader_line 1.6s cubic-bezier(0.4, 0.0, 0.2, 1) infinite, preloader_rotate 1.6s linear infinite;
			animation: preloader_line 1.6s cubic-bezier(0.4, 0.0, 0.2, 1) infinite, preloader_rotate 1.6s linear infinite;
		}
		@-webkit-keyframes preloader_rotate {
			from {	transform: rotate(0); }
			to {	transform: rotate(450deg); }
		}
		@keyframes preloader_rotate {
			from {	transform: rotate(0); }
			to {	transform: rotate(450deg); }
		}
		@-webkit-keyframes preloader_line {
			0% {
				stroke-dasharray: 2, 85.964;
				transform: rotate(0);
			}
			50% {
				stroke-dasharray: 65.973, 21.9911;
				stroke-dashoffset: 0;
			}
			100% {
				stroke-dasharray: 2, 85.964;
				stroke-dashoffset: -65.973;
				transform: rotate(90deg);
			}
		}
		@keyframes preloader_line {
			0% {
				stroke-dasharray: 2, 85.964;
				transform: rotate(0);
			}
			50% {
				stroke-dasharray: 65.973, 21.9911;
				stroke-dashoffset: 0;
			}
			100% {
				stroke-dasharray: 2, 85.964;
				stroke-dashoffset: -65.973;
				transform: rotate(90deg);
			}
		}
	</style>
	<circle id="preloader_spinner" cx="16" cy="16" r="14" fill="none"></circle>
</svg>